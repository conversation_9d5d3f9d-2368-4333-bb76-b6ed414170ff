"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-badge",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.badgeProps,
  setup(__props) {
    const props = __props;
    const content = common_vendor.computed(() => {
      const { modelValue, max, isDot } = props;
      if (isDot)
        return "";
      let value = modelValue;
      if (value && max && common_vendor.isNumber(value) && !Number.isNaN(value) && !Number.isNaN(max)) {
        value = max < value ? `${max}+` : value;
      }
      return value;
    });
    const contentStyle = common_vendor.computed(() => {
      const style = {};
      if (common_vendor.isDef(props.bgColor)) {
        style.backgroundColor = props.bgColor;
      }
      if (common_vendor.isDef(props.top)) {
        style.top = common_vendor.addUnit(props.top);
      }
      if (common_vendor.isDef(props.right)) {
        style.right = common_vendor.addUnit(props.right);
      }
      return common_vendor.objToStyle(style);
    });
    const shouldShowBadge = common_vendor.computed(() => !props.hidden && (content.value || content.value === 0 && props.showZero || props.isDot));
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: shouldShowBadge.value
      }, shouldShowBadge.value ? {
        b: common_vendor.t(content.value),
        c: common_vendor.n(_ctx.type ? "wd-badge__content--" + _ctx.type : ""),
        d: common_vendor.n(_ctx.isDot ? "is-dot" : ""),
        e: common_vendor.s(contentStyle.value)
      } : {}, {
        f: common_vendor.n(_ctx.customClass),
        g: common_vendor.s(_ctx.customStyle)
      });
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-1eb01bf6"]]);
wx.createComponent(Component);
