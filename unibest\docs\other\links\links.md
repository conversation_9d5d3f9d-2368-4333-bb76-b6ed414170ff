# 相关链接

## Unibest Demo 分支演示地址

- [演示地址](https://feige996.github.io/hello-unibest/#/)
- [仓库地址-github](https://github.com/feige996/hello-unibest)
- [仓库地址-gitee](https://gitee.com/feige996/hello-unibest)

## UI 组件库

- [wot-ui](https://wot-design-uni.cn) -- `五星推荐⭐⭐⭐⭐⭐，unibest默认内置`
  > [wot-ui 备用地址](https://wot-design-uni.netlify.app)
- [uni-ui](https://uniapp.dcloud.net.cn/component/uniui/uni-ui.html)
- [uv-ui](https://www.uvui.cn/)
- [uview-plus](https://uiadmin.net/uview-plus/)
- [TuniaoUI](https://vue3.tuniaokj.com/zh-CN/)
- [Sard uniapp](https://sard.wzt.zone/sard-uniapp-docs/)
- [FirstUI](https://doc.firstui.cn/)(部分组件收费)
- [ThorUI](https://thorui.cn/doc/)(部分组件收费)

## 原子类 CSS

- [UnoCSS](https://unocss.dev/) -- `五星推荐⭐⭐⭐⭐⭐`
- [tailwindcss](https://tailwindcss.com/)

## icons

- [icones](https://icones.js.org/) -- `五星推荐⭐⭐⭐⭐⭐` used in `UnoCSS Icons`
- [iconfont](https://www.iconfont.cn/)
- [IconPark](https://iconpark.oceanengine.com)

## 优质组件

- [z-paging](https://z-paging.zxlee.cn/) -- `五星推荐⭐⭐⭐⭐⭐`

  > 一个 `uni-app` 分页组件。
  >
  > 全平台兼容，支持自定义下拉刷新、上拉加载更多，支持虚拟列表，支持自动管理空数据图、点击返回顶部，支持聊天分页、本地分页，支持展示最后更新时间，支持国际化等等。

- [mescroll](https://www.mescroll.com/)

  > 精致的下拉刷新和上拉加载 js 框架，一套代码多端运行，支持 `uni-app`。

## uni-app

- [uni-app 官网](https://uniapp.dcloud.net.cn/)
- [uni-app x 官网](https://doc.dcloud.net.cn/uni-app-x/)

## 图表库

- [ucharts](https://www.ucharts.cn/v2/#/)
- [lime-echart](https://gitee.com/liangei/lime-echart)

> 其他可以在 `uni-app` 插件市场找：[uniapp chart](https://ext.dcloud.net.cn/search?q=chart)

## vue 相关

- [Vue](https://cn.vuejs.org/)
- [Vite](https://cn.vitejs.dev/)
- [Pinia](https://pinia.vuejs.org/zh/)

## 请求库

- [Alova.js](https://alova.js.org/zh-CN)
