/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.gif-box.data-v-a041b13f {
  width: 60rpx;
  height: 60rpx;
}
.card-wrap.data-v-a041b13f {
  padding-top: 80rpx;
}
.card-main.data-v-a041b13f {
  padding-top: 30rpx;
}
.share-box.data-v-a041b13f {
  width: 32rpx;
  height: 32rpx;
}
.logo-box.data-v-a041b13f {
  width: 112rpx;
  height: 112rpx;
  position: absolute;
  top: 0;
  transform: translateY(-60%);
  left: 20rpx;
  z-index: 5;
}
.card-box.data-v-a041b13f {
  width: 120rpx;
  box-sizing: border-box;
  padding: 0;
}
.btn-box.data-v-a041b13f {
  width: 72rpx;
  height: 72rpx;
  box-sizing: border-box;
}
.input.data-v-a041b13f {
  height: 88rpx;
  border-radius: 88rpx;
  background-color: #fff;
  position: relative;
}
.input.disabled.data-v-a041b13f {
  background-color: rgba(255, 255, 255, 0.5);
}
.chat-right.data-v-a041b13f {
  border-top-right-radius: 0;
}
.chat-right.data-v-a041b13f {
  border-top-left-radius: 0;
}
.stop-wrap.data-v-a041b13f {
  height: 30px;
}