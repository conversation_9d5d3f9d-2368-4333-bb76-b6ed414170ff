"use strict";
const common_vendor = require("../common/vendor.js");
require("../store/index.js");
const utils_index = require("../utils/index.js");
const store_user = require("../store/user.js");
const loginRoute = "/pages/login/index";
function isLogined() {
  const userStore = store_user.useUserStore();
  return !!userStore.userInfo.username;
}
function usePageAuth() {
  common_vendor.onLoad((options) => {
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const currentPath = `/${currentPage.route}`;
    let needLoginPages = [];
    {
      needLoginPages = utils_index.getNeedLoginPages();
    }
    const isNeedLogin = needLoginPages.includes(currentPath);
    if (!isNeedLogin) {
      return;
    }
    const hasLogin = isLogined();
    if (hasLogin) {
      return true;
    }
    const queryString = Object.entries(options || {}).map(([key, value]) => `${key}=${encodeURIComponent(String(value))}`).join("&");
    const currentFullPath = queryString ? `${currentPath}?${queryString}` : currentPath;
    const redirectRoute = `${loginRoute}?redirect=${encodeURIComponent(currentFullPath)}`;
    common_vendor.index.redirectTo({ url: redirectRoute });
  });
}
exports.usePageAuth = usePageAuth;
