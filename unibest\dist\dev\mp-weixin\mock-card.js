"use strict";
const pic1 = {
  id: "pic1",
  logoPic: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>",
  name: "<PERSON>",
  company: "EaseGYM健身中心",
  job: "金牌教练",
  profile: "ACE-CPT国际认证教练 + NASM运动营养师，助200+人实现体脂降25%、肌肉增15%，专精增肌塑形+损伤重建，王牌课程3D蜜桃臀 & 代谢轰炸舱，用专业助您高效蜕变！",
  picture: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>",
  avatar: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>",
  videoSrc: "",
  poster: "",
  qrcodepic: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>",
  phoneNumber: "18888888888",
  areacode: "+86",
  email: "<EMAIL>",
  weixin: "lisa@2025",
  address: "浙江省嘉兴市秀洲区",
  latitude: 39.909,
  longitude: 116.39742,
  skills: [
    {
      content: "怎样减脂最有效",
      role: "user",
      id: "0"
    },
    {
      content: "如何练出腹肌",
      role: "user",
      id: "1"
    },
    {
      content: "练多久能看到效果",
      role: "user",
      id: "2"
    }
  ],
  sharePic: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/lisa/<EMAIL>",
  agentId: "1956257023227404289"
};
const pic2 = {
  id: "pic2",
  logoPic: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/linwei/<EMAIL>",
  name: "林薇",
  company: "EaseInsur",
  job: "高级销售经理",
  profile: "我是林薇，EaseInsur高级销售经理，专注车险规划8年，精通UBI定价模型与风险管理，致力于为客户提供优质、全面的保险服务，很高兴为您服务",
  picture: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/linwei/<EMAIL>",
  avatar: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/linwei/<EMAIL>",
  videoSrc: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/18/*********-2a7b6ef3fce2d2d7b59632ec86ead526db1d7da00dc52b9d444908302420b788.mp4",
  poster: "",
  qrcodepic: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/linwei/<EMAIL>",
  phoneNumber: "13866888888",
  areacode: "+86",
  email: "<EMAIL>",
  weixin: "LinWei_EaseInsur",
  address: "浙江省嘉兴市秀洲区",
  latitude: 39.909,
  longitude: 116.39742,
  skills: [
    {
      content: "车险怎么选",
      role: "user",
      id: "0"
    },
    {
      content: "保费怎么算",
      role: "user",
      id: "1"
    },
    {
      content: "出险怎么赔",
      role: "user",
      id: "2"
    }
  ],
  sharePic: "https://demo.easeidea.com/minio/easeidea-ai/2025/08/19/linwei/<EMAIL>",
  agentId: "1957636816988377090"
};
const mockJson = {
  pic1,
  pic2
};
exports.mockJson = mockJson;
