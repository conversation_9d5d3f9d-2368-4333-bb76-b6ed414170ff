"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_config = require("../../api/config.js");
const store_user = require("../../store/user.js");
const mockCard = require("../../mock-card.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
if (!Math) {
  myModal();
}
const myModal = () => "../../components/common/myModal.js";
const KEY_PHONE = "phoneNumber";
const KEY_WEIXIN = "weixin";
const KEY_EMAIL = "email";
const KEY_ADDRESS = "address";
const _sfc_main = {
  __name: "cardDetail",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const cardId = common_vendor.ref("");
    const detail = common_vendor.ref({});
    const videoContext = common_vendor.ref({});
    const isPlay = common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    const safeAreaInsets = common_vendor.ref({});
    const covers = common_vendor.ref([
      {
        id: 0,
        latitude: 39.909,
        longitude: 116.39742,
        width: 32,
        height: 32,
        iconPath: "/static/images/markerIcon.png"
      }
    ]);
    const phoneVisible = common_vendor.ref(false);
    const emailVisible = common_vendor.ref(false);
    const addressVisible = common_vendor.ref(false);
    const weixinVisible = common_vendor.ref(false);
    const hasVideo = common_vendor.computed(() => detail.value.videoSrc != "");
    common_vendor.onReady(() => {
      videoContext.value = common_vendor.index.createVideoContext("myVideo");
      console.log("mockJson", mockCard.mockJson);
    });
    common_vendor.onLoad((e) => {
      cardId.value = e.id || "";
      getInfo();
      common_vendor.index.getSystemInfo({
        success: function(res) {
          safeAreaInsets.value = res.safeAreaInsets;
        }
      });
      common_vendor.index.login({
        // 拿code,请求接口，获取openid 。sessionKey 再请求下一个接口
        provider: "weixin",
        success: (loginRes) => {
          onLogin(loginRes.code);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            icon: "none",
            title: err
          });
        },
        complete: () => {
          console.log("complete");
        }
      });
    });
    common_vendor.onShareAppMessage((e) => {
      let shareInfo = {
        path: `/pages/cardDetail/cardDetail?id=${detail.value.id}`,
        title: `${detail.value.name} - ${detail.value.company}｜${detail.value.job}`,
        imageUrl: detail.value.sharePic
      };
      return shareInfo;
    });
    function _formatPhone(phone = "") {
      let str = phone;
      if (phone.length == 11) {
        const i = 3;
        const y = 7;
        str = phone.slice(0, i) + "-" + phone.slice(i, y) + "-" + phone.slice(y);
      }
      return str;
    }
    function getInfo() {
      detail.value = mockCard.mockJson[cardId.value];
    }
    function onGoback() {
      console.log("点击");
      const pages = getCurrentPages();
      if (pages.length == 1) {
        common_vendor.index.reLaunch({
          url: "/pages/index/index"
        });
      } else {
        common_vendor.index.navigateBack();
      }
    }
    function onTogglePlayStatus(status) {
      isPlay.value = status;
    }
    function onLoadedmetadata(e) {
      onTogglePlayStatus(true);
    }
    function onPlay() {
      onTogglePlayStatus(true);
    }
    function onEnded() {
      onTogglePlayStatus(false);
    }
    function onPause() {
      onTogglePlayStatus(false);
    }
    function onReplay() {
      onTogglePlayStatus(true);
      videoContext.value.play();
    }
    function onLogin(code) {
      return __async(this, null, function* () {
        loading.value = true;
        const reqdata = {
          clientId: userStore.clientId,
          grantType: userStore.grantType,
          tenantId: userStore.tenantId,
          xcxCode: code
        };
        const codeRes = yield $R.post(api_config.apiMap.login.wxLogin, reqdata, { noAuth: true, isJson: true });
        loading.value = false;
        if (codeRes.code === 200) {
          userStore.saveLoginRes(codeRes.data);
        } else {
          common_vendor.index.showToast({
            title: codeRes.msg,
            icon: "none"
          });
        }
      });
    }
    function onOpenChat() {
      if (loading.value)
        return;
      common_vendor.index.navigateTo({
        url: `/pages/chat/chat?id=${detail.value.id}`
      });
    }
    function onTogglePhoneTc(visible) {
      phoneVisible.value = visible;
    }
    function onToggleWeixinTc(visible) {
      weixinVisible.value = visible;
    }
    function onToggleEmailTc(visible) {
      emailVisible.value = visible;
    }
    function onToggleAddresTc(visible) {
      addressVisible.value = visible;
    }
    function onOpenContact(key = "") {
      console.log("key", key);
      switch (key) {
        case KEY_PHONE:
          onTogglePhoneTc(true);
          break;
        case KEY_WEIXIN:
          onToggleWeixinTc(true);
          break;
        case KEY_EMAIL:
          onToggleEmailTc(true);
          break;
        case KEY_ADDRESS:
          onToggleAddresTc(true);
          break;
      }
    }
    function onCallPhone() {
      onTogglePhoneTc(false);
      common_vendor.index.makePhoneCall({
        phoneNumber: detail.value.phoneNumber
      });
    }
    function onOpenLocation() {
      onToggleAddresTc(false);
      common_vendor.index.openLocation({
        latitude: detail.value.latitude,
        longitude: detail.value.longitude,
        success: function() {
        }
      });
    }
    function onCopy(key) {
      let data = detail.value[key];
      console.log("data", data);
      common_vendor.index.setClipboardData({
        data,
        success: function() {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "none"
          });
        }
      });
    }
    function onSelectPrequestion(item = {}) {
      if (loading.value)
        return;
      common_vendor.index.navigateTo({
        url: `/pages/chat/chat?question=${JSON.stringify(item)}&id=${detail.value.id}`
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: hasVideo.value
      }, hasVideo.value ? {
        b: detail.value.picture,
        c: common_vendor.n(isPlay.value ? "animate__fadeOut" : "animate__fadeIn")
      } : {
        d: detail.value.picture
      }, {
        e: hasVideo.value
      }, hasVideo.value ? {
        f: detail.value.videoSrc,
        g: detail.value.poster,
        h: common_vendor.o(onPlay),
        i: common_vendor.o(onEnded),
        j: common_vendor.o(onPause),
        k: common_vendor.o(onLoadedmetadata)
      } : {}, {
        l: common_vendor.o(onGoback),
        m: detail.value.logoPic,
        n: hasVideo.value
      }, hasVideo.value ? common_vendor.e({
        o: !isPlay.value
      }, !isPlay.value ? {
        p: common_assets._imports_0,
        q: common_vendor.o(onReplay)
      } : {}) : {}, {
        r: common_assets._imports_1,
        s: common_vendor.t(detail.value.name),
        t: common_vendor.t(detail.value.company),
        v: common_vendor.t(detail.value.job),
        w: common_assets._imports_2$1,
        x: common_assets._imports_3,
        y: common_vendor.t(detail.value.areacode),
        z: common_vendor.t(_formatPhone(detail.value.phoneNumber)),
        A: common_vendor.o(($event) => onOpenContact(KEY_PHONE)),
        B: common_assets._imports_4,
        C: common_vendor.t(detail.value.weixin),
        D: common_vendor.o(($event) => onOpenContact(KEY_WEIXIN)),
        E: common_assets._imports_5,
        F: common_vendor.t(detail.value.email),
        G: common_vendor.o(($event) => onOpenContact(KEY_EMAIL)),
        H: common_assets._imports_6,
        I: common_vendor.t(detail.value.address),
        J: common_vendor.o(($event) => onOpenContact(KEY_ADDRESS)),
        K: common_vendor.t(detail.value.profile),
        L: common_vendor.f(detail.value.skills, (item, index, i0) => {
          return {
            a: common_vendor.t(item.content),
            b: index,
            c: common_vendor.o(($event) => onSelectPrequestion(item), index)
          };
        }),
        M: common_vendor.n(loading.value ? "opacity-0_5" : ""),
        N: loading.value
      }, loading.value ? {
        O: common_assets._imports_2
      } : {}, {
        P: common_vendor.n(loading.value ? "opacity-0_5" : ""),
        Q: safeAreaInsets.value.bottom + "px",
        R: common_vendor.o(onOpenChat),
        S: phoneVisible.value
      }, phoneVisible.value ? {
        T: common_vendor.t(detail.value.areacode),
        U: common_vendor.t(detail.value.phoneNumber),
        V: common_vendor.o(($event) => onCopy(KEY_PHONE)),
        W: common_vendor.o(onCallPhone),
        X: common_vendor.o(($event) => onTogglePhoneTc(false)),
        Y: common_vendor.p({
          ["round-icon"]: "/static/images/<EMAIL>",
          title: "联系电话",
          desc: "这是我的手机号码，欢迎您来电垂询"
        })
      } : {}, {
        Z: weixinVisible.value
      }, weixinVisible.value ? {
        aa: common_vendor.t(detail.value.weixin),
        ab: common_assets._imports_8$1,
        ac: common_vendor.o(($event) => onCopy(KEY_WEIXIN)),
        ad: detail.value.avatar,
        ae: common_vendor.t(detail.value.name),
        af: common_vendor.t(detail.value.address),
        ag: detail.value.qrcodepic,
        ah: common_vendor.o(($event) => onToggleWeixinTc(false)),
        ai: common_vendor.p({
          ["custom-title"]: true
        })
      } : {}, {
        aj: emailVisible.value
      }, emailVisible.value ? {
        ak: common_vendor.t(detail.value.email),
        al: common_vendor.o(($event) => onCopy(KEY_EMAIL)),
        am: common_vendor.o(($event) => onToggleEmailTc(false)),
        an: common_vendor.p({
          ["round-icon"]: "/static/images/<EMAIL>",
          title: "邮箱",
          desc: "期待收到您的来信"
        })
      } : {}, {
        ao: addressVisible.value
      }, addressVisible.value ? {
        ap: common_vendor.t(detail.value.address),
        aq: common_vendor.t(detail.value.address),
        ar: detail.value.latitude,
        as: detail.value.longitude,
        at: covers.value,
        av: common_vendor.o(($event) => onCopy(KEY_ADDRESS)),
        aw: common_vendor.o(onOpenLocation),
        ax: common_vendor.o(($event) => onToggleAddresTc(false)),
        ay: common_vendor.p({
          ["round-icon"]: "/static/images/<EMAIL>",
          title: "公司地址",
          desc: "这是详细地址导航，欢迎您前往"
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-d1a3008d"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
