<template>
  <view v-if="visible" class="modal-overlay" @tap="onClose">
    <view class="modal-content" @tap.stop>
      <slot></slot>
    </view>
  </view>
</template>

<script setup>
interface Props {
  visible: boolean
}

interface Emits {
  (e: 'close'): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

function onClose() {
  emit('close')
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin: 20px;
  max-width: 90%;
  max-height: 80%;
  overflow-y: auto;
}
</style>
