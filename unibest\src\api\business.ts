import { http } from '@/utils/request'
import { apiMap, sys } from './config'

// 登录相关接口
export interface LoginParams {
  code: string
  clientId: string
  grantType: string
  tenantId: string
}

export interface LoginResponse {
  access_token: string
  client_id: string
  expires_in: number
  refresh_token: string
  token_type: string
}

// 微信登录
export function wxLogin(data: LoginParams) {
  return http.post<LoginResponse>(apiMap.login.wxLogin, data)
}

// 会话相关接口
export interface ConversationParams {
  agentId: string
}

export interface ConversationResponse {
  id: string
  agentId: string
  userId: string
  status: string
  createTime: string
}

// 创建或获取会话
export function getOrCreateConversation(data: ConversationParams) {
  return http.post<ConversationResponse>(apiMap.conversation.getOrCreateByAgent, data)
}

// 获取会话历史记录
export function getConversationRecord(conversationId: string) {
  return http.get(`${apiMap.conversation.record}/${conversationId}`)
}

// 发起会话
export interface CompletionsParams {
  conversationId: string
  message: string
  stream?: boolean
}

export function sendMessage(data: CompletionsParams) {
  return http.post(apiMap.conversation.completions, data)
}

// 关闭会话
export function closeConversation(conversationId: string) {
  return http.post(`${apiMap.conversation.close}/${conversationId}`)
}

// 获取WebSocket地址
export function getWebSocketUrl() {
  return `${sys.host.replace('https', 'wss')}/${apiMap.conversation.websocket}`
}
