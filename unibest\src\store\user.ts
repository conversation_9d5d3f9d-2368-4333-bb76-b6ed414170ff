import { computed, ref } from 'vue'
import { defineStore } from 'pinia'

// 获取本地存储的登录信息
const _loginRes = uni.getStorageSync('loginRes') ? uni.getStorageSync('loginRes') : {}

export const useUserStore = defineStore(
  'user',
  () => {
    const loginRes = ref(_loginRes)
    const clientId = ref('e5cd7e4891bf95d1d19206ce24a7b32e')
    const grantType = ref('xcx')
    const tenantId = ref('000000')
    // const agentId = ref('1956257023227404289') // 助手id

    const accessToken = computed(() => {
      return loginRes.value.access_token
    })

    const isLogin = computed(() => !!loginRes.value.access_token)

    function saveLoginRes(obj: any) {
      loginRes.value = obj
      uni.setStorageSync('loginRes', obj)
    }

    function clearStorage() {
      loginRes.value = {}
      uni.removeStorageSync('loginRes')
    }

    const login = async (data: any) => {
      saveLoginRes(data)
    }

    const logout = () => {
      clearStorage()
    }

    return {
      loginRes,
      clientId,
      grantType,
      tenantId,
      accessToken,
      isLogin,
      saveLoginRes,
      clearStorage,
      login,
      logout,
    }
  },
  {
    persist: {
      key: 'user',
    },
  },
)
