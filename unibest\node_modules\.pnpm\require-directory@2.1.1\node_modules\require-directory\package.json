{"author": "<PERSON> <<EMAIL>> (http://github.com/troygoode/)", "name": "require-directory", "version": "2.1.1", "description": "Recursively iterates over specified directory, require()'ing each file, and returning a nested hash structure containing those modules.", "keywords": ["require", "directory", "library", "recursive"], "homepage": "https://github.com/troygoode/node-require-directory/", "main": "index.js", "repository": {"type": "git", "url": "git://github.com/troygoode/node-require-directory.git"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "web": "http://github.com/troygoode/"}], "license": "MIT", "bugs": {"url": "http://github.com/troygoode/node-require-directory/issues/"}, "engines": {"node": ">=0.10.0"}, "devDependencies": {"jshint": "^2.6.0", "mocha": "^2.1.0"}, "scripts": {"test": "mocha", "lint": "jshint index.js test/test.js"}}