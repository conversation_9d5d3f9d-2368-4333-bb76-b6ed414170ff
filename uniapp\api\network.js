import { sys, apiMap } from './config'
import { createP<PERSON> } from 'pinia'
import { createApp } from 'vue'
import App from '../App.vue'
import { useUserStore } from '@/store/user'

const pinia = createPinia()
const app = createApp(App)
app.use(pinia)
const userStore = useUserStore()
console.log('userStore', userStore)
const _request = function (options) {
  const loginRes = uni.getStorageSync('loginRes') ? uni.getStorageSync('loginRes') : {}
  const access_token = loginRes?.access_token
  options.timeout = sys.timeout
  options.header = {
    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
    'Cache-Control': 'no-cache',
  }
  if (options.isJson) {
    options.header['Content-Type'] = 'application/json'
  }
  if (options.isFormData) {
    options.header['Content-Type'] = 'multipart/form-data'
  }
  if (!options.noAuth) {
    // 默认需要权限，  token 过期则需要跳转到自动登录
    if (!access_token) {
      uni.showToast({
        title: '无效token,请登录',
        icon: 'none',
        duration: 2500,
      })
      setTimeout(() => {
        return uni.reLaunch({
          url: '/pages/cardDetail/cardDetail',
        })
      }, 2500)
    }
    options.header.Authorization = 'Bearer ' + access_token
    options.header.Clientid = loginRes?.client_id
  }

  return new Promise((resolve, reject) => {
    const task = uni.request({
      ...options,
      success(res) {
        if (res.statusCode !== 200) {
          // 接口请求出错
          console.log('request-success-error:', res)
          uni.showToast({
            icon: 'none',
            title: `请求出错`,
            duration: 2000,
          })
        }
        // 需要源数据返回
        if (options.native) {
          return resolve(res)
        }
        // token 过期
        if (res.data.code == 401) {
          uni.showToast({
            icon: 'none',
            title: `token过期，正在为你跳转到自动登录`,
            duration: 2000,
          })
          userStore.clearStorage()
          uni.redirectTo({
            url: `/pages/cardDetail/cardDetail`,
          })
        }
        // 返回数据
        return resolve(res.data)
      },
      fail(e) {
        console.log('request-fail', e)
      },
      complete: () => {},
    })
  })
}

/**
 *
 * @param {*} key  地址
 * @param {*} params  请求参数
 * @param {*} method  请求方法， 权限
 */
const request = function (key, data = {}, method, options = {}) {
  let ifRight = key.indexOf('.')
  let url = ''
  if (ifRight != -1) {
    const keys = key.split('.')
    url = keys.reduce((sum, k) => {
      if (k) {
        sum = sum[k]
      }
      return sum
    }, apiMap)
  } else {
    url = key
  }
  url = url.includes('http') ? url : sys.host + url
  options.url = options.urlpj ? sys.host + options.url : url
  options.data = data
  options.method = method || 'GET'
  return _request(options)
}

const get = (url, data = {}, options = {}) => {
  return request(url, data, 'GET', options)
}
const post = (url, data = {}, options = {}) => {
  return request(url, data, 'POST', options)
}

export default {
  request,
  get,
  post,
}
