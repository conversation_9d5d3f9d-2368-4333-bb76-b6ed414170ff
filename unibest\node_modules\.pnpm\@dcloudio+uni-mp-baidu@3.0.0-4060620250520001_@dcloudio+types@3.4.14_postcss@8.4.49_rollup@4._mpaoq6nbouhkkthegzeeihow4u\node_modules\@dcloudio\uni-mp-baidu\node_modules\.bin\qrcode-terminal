#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/qrcode-terminal@0.12.0/node_modules/qrcode-terminal/bin/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/qrcode-terminal@0.12.0/node_modules/qrcode-terminal/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/qrcode-terminal@0.12.0/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/qrcode-terminal@0.12.0/node_modules/qrcode-terminal/bin/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/qrcode-terminal@0.12.0/node_modules/qrcode-terminal/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/qrcode-terminal@0.12.0/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../qrcode-terminal@0.12.0/node_modules/qrcode-terminal/bin/qrcode-terminal.js" "$@"
else
  exec node  "$basedir/../../../../../../qrcode-terminal@0.12.0/node_modules/qrcode-terminal/bin/qrcode-terminal.js" "$@"
fi
