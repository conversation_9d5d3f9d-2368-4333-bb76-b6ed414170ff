"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
function showToast(options) {
  const defaultOptions = {
    type: "info",
    duration: 2e3,
    position: "middle",
    message: ""
  };
  const mergedOptions = typeof options === "string" ? __spreadProps(__spreadValues({}, defaultOptions), { message: options }) : __spreadValues(__spreadValues({}, defaultOptions), options);
  const positionMap = {
    top: "top",
    middle: "center",
    bottom: "bottom"
  };
  const iconMap = {
    success: "success",
    error: "error",
    warning: "fail",
    info: "none"
  };
  common_vendor.index.showToast({
    title: mergedOptions.message,
    duration: mergedOptions.duration,
    position: positionMap[mergedOptions.position],
    icon: mergedOptions.icon || iconMap[mergedOptions.type],
    mask: true
  });
}
const toast = {
  success: (message, options) => showToast(__spreadProps(__spreadValues({}, options), { type: "success", message })),
  error: (message, options) => showToast(__spreadProps(__spreadValues({}, options), { type: "error", message })),
  warning: (message, options) => showToast(__spreadProps(__spreadValues({}, options), { type: "warning", message })),
  info: (message, options) => showToast(__spreadProps(__spreadValues({}, options), { type: "info", message }))
};
exports.toast = toast;
