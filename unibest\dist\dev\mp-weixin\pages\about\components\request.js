"use strict";
const common_vendor = require("../../../common/vendor.js");
const service_index_foo = require("../../../service/index/foo.js");
const hooks_useRequest = require("../../../hooks/useRequest.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  _easycom_wd_button2();
}
const _easycom_wd_button = () => "../../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
if (!Math) {
  _easycom_wd_button();
}
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent({
  __name: "request",
  setup(__props) {
    const recommendUrl = common_vendor.ref("http://laf.run/signup?code=ohaOgIX");
    const initialData = void 0;
    const { loading, data, run } = hooks_useRequest.useRequest(() => service_index_foo.getFooAPI("菲鸽"), {
      immediate: true,
      initialData
    });
    function reset() {
      data.value = initialData;
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.t(common_vendor.unref(recommendUrl)),
        b: common_vendor.o(common_vendor.unref(run)),
        c: common_vendor.unref(loading)
      }, common_vendor.unref(loading) ? {} : {
        d: common_vendor.t(JSON.stringify(common_vendor.unref(data)))
      }, {
        e: common_vendor.o(reset),
        f: common_vendor.p({
          type: "error",
          disabled: !common_vendor.unref(data)
        })
      });
    };
  }
});
wx.createComponent(_sfc_main);
