;((window._iconfont_svg_string_4991172 =
  '<svg><symbol id="icon-arrow-left" viewBox="0 0 1024 1024"><path d="M672 896a32 32 0 0 0 22.624-9.376 32 32 0 0 0 0-45.248L365.248 512l329.376-329.376a32 32 0 0 0 0-45.248 32 32 0 0 0-45.248 0l-352 352a32 32 0 0 0 0 45.248l352 352A32 32 0 0 0 672 896z"  ></path></symbol><symbol id="icon-xiala" viewBox="0 0 1640 1024"><path d="M1605.057536 219.0336L903.412736 986.3168a110.4896 110.4896 0 0 1-165.9904 0L35.777536 219.0336A130.1504 130.1504 0 0 1 118.721536 0h1402.88a130.1504 130.1504 0 0 1 83.456 219.0336z" fill="#FF5300" ></path></symbol><symbol id="icon-fanhui-" viewBox="0 0 1024 1024"><path d="M656.59 854.73l-328.32-328a22.8 22.8 0 0 1 0-32.24L655.53 166.9a22.8 22.8 0 0 0 0-32.24 22.8 22.8 0 0 0-32.24 0L263 495.25a22.8 22.8 0 0 0 0 32.24l360.59 360.27a22.8 22.8 0 0 0 32.24 0l0.78-0.78a22.8 22.8 0 0 0-0.02-32.25z" fill="#9D9D9D" ></path></symbol><symbol id="icon-fanhui" viewBox="0 0 1024 1024"><path d="M918.8 411.9c-61.6-45.5-146.1-69.6-244.5-69.6H53.4L311.9 130c9.5-7.8 10.9-21.8 3.1-31.3-7.8-9.5-21.8-10.9-31.3-3.1L24 308.8l-1.6 1.5C8 324.7 0 344 0 364.6s8 39.9 22.4 54.3l0.8 0.8 260.4 213.9c4.1 3.4 9.1 5.1 14.1 5.1 6.4 0 12.8-2.8 17.2-8.1 7.8-9.5 6.4-23.5-3.1-31.3L53.4 386.9h620.9c147.5 0 305.2 63.9 305.2 243.3 0 178.7-153.3 258.8-305.2 258.8H328.8c-12.3 0-22.3 10-22.3 22.3s10 22.3 22.3 22.3h345.5c95.7 0 182.8-27.7 245.2-77.9 68.4-55 104.5-132.9 104.5-225.4 0-92-36.4-167.5-105.2-218.4z" fill="" ></path></symbol></svg>'),
  ((n) => {
    var t = (e = (e = document.getElementsByTagName('script'))[e.length - 1]).getAttribute(
        'data-injectcss',
      ),
      e = e.getAttribute('data-disable-injectsvg')
    if (!e) {
      var i,
        o,
        a,
        l,
        c,
        d = function (t, e) {
          e.parentNode.insertBefore(t, e)
        }
      if (t && !n.__iconfont__svg__cssinject__) {
        n.__iconfont__svg__cssinject__ = !0
        try {
          document.write(
            '<style>.svgfont {display: inline-block;width: 1em;height: 1em;fill: currentColor;vertical-align: -0.1em;font-size:16px;}</style>',
          )
        } catch (t) {
          console && console.log(t)
        }
      }
      ;((i = function () {
        var t,
          e = document.createElement('div')
        ;((e.innerHTML = n._iconfont_svg_string_4991172),
          (e = e.getElementsByTagName('svg')[0]) &&
            (e.setAttribute('aria-hidden', 'true'),
            (e.style.position = 'absolute'),
            (e.style.width = 0),
            (e.style.height = 0),
            (e.style.overflow = 'hidden'),
            (e = e),
            (t = document.body).firstChild ? d(e, t.firstChild) : t.appendChild(e)))
      }),
        document.addEventListener
          ? ~['complete', 'loaded', 'interactive'].indexOf(document.readyState)
            ? setTimeout(i, 0)
            : ((o = function () {
                ;(document.removeEventListener('DOMContentLoaded', o, !1), i())
              }),
              document.addEventListener('DOMContentLoaded', o, !1))
          : document.attachEvent &&
            ((a = i),
            (l = n.document),
            (c = !1),
            r(),
            (l.onreadystatechange = function () {
              'complete' == l.readyState && ((l.onreadystatechange = null), s())
            })))
    }
    function s() {
      c || ((c = !0), a())
    }
    function r() {
      try {
        l.documentElement.doScroll('left')
      } catch (t) {
        return void setTimeout(r, 50)
      }
      s()
    }
  })(window))
