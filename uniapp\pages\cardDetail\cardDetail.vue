<template>
  <view class="height-100vh flex flex-column box-border page-wrap">
    <view class="position-relative bgWrap zindex-5 flex-1 flex">
      <view class="position-relative bg-content flex-1 width-100">
        <view class="position-absolute left-0 top-0 right-0 bottom-0 video-wrap">
          <view
            v-if="hasVideo"
            class="video-box img"
            :class="[isPlay ? 'animate__fadeOut' : 'animate__fadeIn']"
          >
            <image
              :src="detail.picture"
              lazy-load="false"
              mode="aspectFill"
              class="width-100 height-100"
            ></image>
          </view>
          <image
            v-else
            :src="detail.picture"
            lazy-load="false"
            mode="aspectFill"
            class="width-100 height-100"
          ></image>
          <view v-if="hasVideo" class="video-box video">
            <video
              id="myVideo"
              :src="detail.videoSrc"
              autoplay
              enable-play-gesture
              object-fit="cover"
              :poster="detail.poster"
              class="width-100 height-100"
              @play="onPlay"
              @ended="onEnded"
              @pause="onPause"
              @loadedmetadata="onLoadedmetadata"
            ></video>
          </view>
        </view>
      </view>
      <view
        class="position-absolute left-0 top-0 right-0 bottom-0 zindex-10 text-white flex flex-column bg-content-main"
      >
        <view
          class="flex align-center page-title-box flex-shrink text-center flex align-center justify-center position-relative"
        >
          <view
            @tap="onGoback"
            class="mt ml flex-shrink font-12 px-2 py-1 position-absolute left-0 text-white"
          >
            <view class="iconfont icon-fanhui"></view>
          </view>
          <text class="font-weight-500 font-m flex-1">我的名片</text>
        </view>
        <view class="p-base flex-1 flex flex-column pt-5">
          <view class="flex justify-between flex-shrink">
            <view class="logo-box mr-base">
              <image :src="detail.logoPic" mode="aspectFit" class="width-100 height-100"></image>
            </view>
          </view>
          <view class="flex-1 flex flex-column justify-end">
            <template v-if="hasVideo">
              <view v-if="!isPlay" class="replay-box mb-2" hover-class="bg-hover" @tap="onReplay">
                <image
                  src="/static/images/<EMAIL>"
                  mode="aspectFit"
                  class="width-100 height-100"
                ></image>
              </view>
            </template>
          </view>
          <view class="mb-auto flex-shrink flex align-center position-relative">
            <view class="more-box mr-4">
              <image
                src="/static/images/<EMAIL>"
                mode="aspectFit"
                class="width-100 height-100"
              ></image>
            </view>
            <view>
              <view class="font-weight-bold font-20 mb">{{ detail.name }}</view>
              <view class="flex align-center font-14">
                {{ detail.company }}
                <span class="mx-1">|</span>
                {{ detail.job }}
              </view>
            </view>
            <button
              open-type="share"
              class="share-box position-absolute flex align-center justify-center font-12"
            >
              <view class="icon-box mr-0-8 flex algin-center">
                <image
                  src="@/static/images/<EMAIL>"
                  mode="aspectFit"
                  class="width-100 height-100"
                ></image>
              </view>
              分享名片
            </button>
          </view>
        </view>
      </view>
    </view>
    <view class="p-4 width-100 box-border flex-shrink">
      <view class="scroll-row pb-4 overflow-x-auto no-scrollbar box-border">
        <view class="d-inline-block mr-4" @click="onOpenContact(KEY_PHONE)">
          <view
            class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item"
          >
            <view class="flex align-center mb-1">
              <view class="icon-box mr-0-8">
                <image
                  src="/static/images/<EMAIL>"
                  mode="aspectFit"
                  class="width-100 height-100"
                ></image>
              </view>
              <view class="text-theme-brand font-14">联系电话</view>
            </view>
            <view class="text-third width-100 text-ellipsis font-12">
              <text class="mr-1">{{ detail.areacode }}</text>
              {{ _formatPhone(detail.phoneNumber) }}
            </view>
          </view>
        </view>
        <view class="d-inline-block mr-4" @click="onOpenContact(KEY_WEIXIN)">
          <view
            class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item"
          >
            <view class="flex align-center mb-1">
              <view class="icon-box mr-0-8">
                <image
                  src="/static/images/<EMAIL>"
                  mode="aspectFit"
                  class="width-100 height-100"
                ></image>
              </view>
              <view class="text-theme-brand font-14">微信</view>
            </view>
            <view class="text-third width-100 text-ellipsis font-12">
              {{ detail.weixin }}
            </view>
          </view>
        </view>
        <view class="d-inline-block mr-4" @click="onOpenContact(KEY_EMAIL)">
          <view
            class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item"
          >
            <view class="flex align-center mb-1">
              <view class="icon-box mr-0-8">
                <image
                  src="/static/images/<EMAIL>"
                  mode="aspectFit"
                  class="width-100 height-100"
                ></image>
              </view>
              <view class="text-theme-brand font-14">邮箱</view>
            </view>
            <view class="text-third width-100 text-ellipsis font-12">
              {{ detail.email }}
            </view>
          </view>
        </view>
        <view class="d-inline-block" @click="onOpenContact(KEY_ADDRESS)">
          <view
            class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item"
          >
            <view class="flex align-center mb-1">
              <view class="icon-box mr-0-8">
                <image
                  src="/static/images/<EMAIL>"
                  mode="aspectFit"
                  class="width-100 height-100"
                ></image>
              </view>
              <view class="text-theme-brand font-14">公司地址</view>
            </view>
            <view class="text-third width-100 text-ellipsis font-12">
              {{ detail.address }}
            </view>
          </view>
        </view>
      </view>
      <view class="text-third mb-4 font-12">
        {{ detail.profile }}
      </view>
      <view class="scroll-row mb-4 overflow-x-auto no-scrollbar">
        <view
          v-for="(item, index) in detail.skills"
          :key="index"
          class="rounded-80 py-1 px-3 text-second bg-info font-12 mr-base mb scroll-row-item"
          :class="loading ? 'opacity-0_5' : ''"
          @tap="onSelectPrequestion(item)"
        >
          {{ item.content }}
        </view>
      </view>
      <view
        class="bg-theme-brand text-white text-center normal-button-rounded flex align-center justify-center font-14"
        :style="{ marginBottom: safeAreaInsets.bottom + 'px' }"
        @click="onOpenChat"
      >
        <view v-if="loading" class="loading-box">
          <image
            src="/static/images/loading.gif"
            mode="aspectFit"
            class="width-100 height-100"
          ></image>
        </view>
        <view class="flex align-center" :class="loading ? 'opacity-0_5' : ''">
          <text class="mx-0-8">AI</text>
          聊一聊
        </view>
      </view>
    </view>
    <!-- 弹窗 -->
    <!-- 电话 -->
    <my-modal
      v-if="phoneVisible"
      @close="onTogglePhoneTc(false)"
      round-icon="/static/images/<EMAIL>"
      title="联系电话"
      desc="这是我的手机号码，欢迎您来电垂询"
    >
      <view
        class="my-4 bg-input contact-input text-first font-weight-bold font-18 flex align-center justify-center"
      >
        <text>{{ detail.areacode }}</text>
        <view class="line"></view>
        <text>{{ detail.phoneNumber }}</text>
      </view>
      <view class="flex align-center justify-between">
        <view
          class="normal-button-rounded text-theme-brand border-current flex-1 font-14"
          @click="onCopy(KEY_PHONE)"
          >复制号码</view
        >
        <view class="mx-2 flex-shrink"></view>
        <view
          class="normal-button-rounded bg-theme-brand text-white flex-1 font-14"
          @click="onCallPhone"
          >拨打号码</view
        >
      </view>
    </my-modal>
    <!-- 微信 -->
    <my-modal custom-title v-if="weixinVisible" @close="onToggleWeixinTc(false)">
      <template #titleLeft>
        <view class="flex align-center font-14">
          <view class="text-<strong></strong>second mr-base">微信号</view>
          <view class="text-first font-weight-bold mr-base">{{ detail.weixin }}</view>
          <view class="copyicon-box mr-1" @click="onCopy(KEY_WEIXIN)">
            <image
              src="/static/images/<EMAIL>"
              mode="aspectFit"
              class="width-100 height-100"
            ></image>
          </view>
        </view>
      </template>
      <view class="pt-4">
        <view class="flex align-center mb-4">
          <view class="avatar-box mr-4 flex-shrink">
            <image :src="detail.avatar" mode="aspectFit" class="width-100 height-100"></image>
          </view>
          <view class="flex-1">
            <view class="font-weight-bold text-first font-20 my-0-8">{{ detail.name }}</view>
            <view class="text-second font-14">{{ detail.address }}</view>
          </view>
        </view>
        <view class="rounded-8 bg-info p-4">
          <view class="rounded-8 bg-white p-4 mb-4 flex align-center">
            <view class="qrcode-box mx-auto">
              <image
                :src="detail.qrcodepic"
                mode="aspectFit"
                class="width-100 height-100"
                show-menu-by-longpress
              ></image>
            </view>
          </view>
          <view class="text-placeholder font-12 text-center">扫描二维码，添加我的企业微信</view>
        </view>
      </view>
    </my-modal>
    <!-- 邮箱 -->
    <my-modal
      v-if="emailVisible"
      @close="onToggleEmailTc(false)"
      round-icon="/static/images/<EMAIL>"
      title="邮箱"
      desc="期待收到您的来信"
    >
      <view
        class="my-4 bg-input contact-input text-first font-weight-bold font-18 flex align-center justify-center"
      >
        <text>{{ detail.email }}</text>
      </view>
      <view class="flex align-center justify-center">
        <view
          class="normal-button-rounded text-theme-brand border-current width-100 font-14"
          @click="onCopy(KEY_EMAIL)"
          >复制邮箱</view
        >
      </view>
    </my-modal>
    <!-- 地址 -->
    <my-modal
      v-if="addressVisible"
      @close="onToggleAddresTc(false)"
      round-icon="/static/images/<EMAIL>"
      title="公司地址"
      desc="这是详细地址导航，欢迎您前往"
    >
      <view class="my-4 bg-light-detail rounded-8 overflow-hidden">
        <view class="p-1-2">
          <view class="text-first font-16 font-weight-bold mb-0-8">{{ detail.address }}</view>
          <view class="text-second font-14">
            {{ detail.address }}
          </view>
        </view>
        <view class="address-box">
          <map
            style="width: 100%; height: 100%"
            :latitude="detail.latitude"
            :longitude="detail.longitude"
            :markers="covers"
          ></map>
        </view>
      </view>
      <view class="flex align-center justify-between">
        <view
          class="normal-button-rounded text-theme-brand border-current flex-1 font-14"
          @click="onCopy(KEY_ADDRESS)"
          >复制地址</view
        >
        <view class="mx-2 flex-shrink"></view>
        <view
          class="normal-button-rounded bg-theme-brand text-white flex-1 font-14"
          @click="onOpenLocation"
          >前端导航</view
        >
      </view>
    </my-modal>
  </view>
</template>

<script setup>
import { reactive, ref, computed } from 'vue'
import { onShareAppMessage, onLoad, onReady } from '@dcloudio/uni-app'
import myModal from '@/components/common/myModal.vue'
import { apiMap, $R } from '@/api/config'
import { useUserStore } from '@/store/user'
import mockJson from '@/api/mock-card.json'

/*常量*/
const KEY_PHONE = 'phoneNumber'
const KEY_WEIXIN = 'weixin'
const KEY_EMAIL = 'email'
const KEY_ADDRESS = 'address'
// const CONTACT_LIST = [
// 	{ title: '联系电话', desc: '188-8888-8888', icon: PHONE_ICON, key: KEY_PHONE },
// 	{ title: '微信', desc: 'lisa@2025', icon: WECHAT_ICON, key: KEY_WEIXIN },
// 	{ title: '邮箱', desc: '<EMAIL>', icon: EMAIL_ICON, key: KEY_EMAIL },
// 	{ title: '公司地址', desc: '浙江省嘉兴市秀洲区', icon: ADDRESS_ICON, key: KEY_ADDRESS }
// ];

/*变量*/
const userStore = useUserStore()
const cardId = ref('')
const detail = ref({})
const videoContext = ref({}) // 视频上下文
const isPlay = ref(false) // 视频播放中
const loading = ref(false) // 接口请求中
const safeAreaInsets = ref({}) // 兼容刘海屏

const covers = ref([
  {
    id: 0,
    latitude: 39.909,
    longitude: 116.39742,
    width: 32,
    height: 32,
    iconPath: '/static/images/markerIcon.png',
  },
]) // 地图标记点
const phoneVisible = ref(false) // 电话弹窗
const emailVisible = ref(false) // email弹窗
const addressVisible = ref(false) // address弹窗
const weixinVisible = ref(false) // 微信弹窗
const hasVideo = computed(() => detail.value.videoSrc != '')
/*钩子*/
onReady(() => {
  videoContext.value = uni.createVideoContext('myVideo')
  console.log('mockJson', mockJson)
})
onLoad((e) => {
  cardId.value = e.id || ''
  getInfo()
  uni.getSystemInfo({
    success: function (res) {
      safeAreaInsets.value = res.safeAreaInsets
    },
  })
  uni.login({
    // 拿code,请求接口，获取openid 。sessionKey 再请求下一个接口
    provider: 'weixin',
    success: (loginRes) => {
      onLogin(loginRes.code)
    },
    fail: (err) => {
      uni.showToast({
        icon: 'none',
        title: err,
      })
    },
    complete: () => {
      console.log('complete')
    },
  })
})
//用户点击分享
onShareAppMessage((e) => {
  let shareInfo = {
    path: `/pages/cardDetail/cardDetail?id=${detail.value.id}`,
    title: `${detail.value.name} - ${detail.value.company}｜${detail.value.job}`,
    imageUrl: detail.value.sharePic,
  }
  return shareInfo
})
/*函数*/
// 格式化手机格式
function _formatPhone(phone = '') {
  let str = phone
  if (phone.length == 11) {
    const i = 3
    const y = 7
    str = phone.slice(0, i) + '-' + phone.slice(i, y) + '-' + phone.slice(y)
  }
  return str
}
// 获取用户数据
function getInfo() {
  detail.value = mockJson[cardId.value]
  // console.log("detail.value",detail.value)
}
function onGoback() {
  console.log('点击')
  const pages = getCurrentPages()
  if (pages.length == 1) {
    uni.reLaunch({
      url: '/pages/index/index',
    })
  } else {
    uni.navigateBack()
  }
}
// 视频播放事件
function onTogglePlayStatus(status) {
  isPlay.value = status
}
function onLoadedmetadata(e) {
  onTogglePlayStatus(true)
}
function onPlay() {
  onTogglePlayStatus(true)
}
function onEnded() {
  onTogglePlayStatus(false)
}
function onPause() {
  onTogglePlayStatus(false)
}
function onReplay() {
  onTogglePlayStatus(true)
  videoContext.value.play()
}
// 获取token
async function onLogin(code) {
  loading.value = true
  const reqdata = {
    clientId: userStore.clientId,
    grantType: userStore.grantType,
    tenantId: userStore.tenantId,
    xcxCode: code,
  }
  const codeRes = await $R.post(apiMap.login.wxLogin, reqdata, { noAuth: true, isJson: true })
  loading.value = false
  if (codeRes.code === 200) {
    userStore.saveLoginRes(codeRes.data)
  } else {
    uni.showToast({
      title: codeRes.msg,
      icon: 'none',
    })
  }
}
// 跳转到聊天
function onOpenChat() {
  if (loading.value) return
  uni.navigateTo({
    url: `/pages/chat/chat?id=${detail.value.id}`,
  })
}
//弹窗打开
function onTogglePhoneTc(visible) {
  phoneVisible.value = visible
}
function onToggleWeixinTc(visible) {
  weixinVisible.value = visible
}
function onToggleEmailTc(visible) {
  emailVisible.value = visible
}
function onToggleAddresTc(visible) {
  addressVisible.value = visible
}
// 获取聊天方式
function onOpenContact(key = '') {
  console.log('key', key)
  switch (key) {
    case KEY_PHONE:
      onTogglePhoneTc(true)
      break
    case KEY_WEIXIN:
      onToggleWeixinTc(true)
      break
    case KEY_EMAIL:
      onToggleEmailTc(true)
      break
    case KEY_ADDRESS:
      onToggleAddresTc(true)
      break
  }
}
// 打电话
function onCallPhone() {
  onTogglePhoneTc(false)
  uni.makePhoneCall({
    phoneNumber: detail.value.phoneNumber,
  })
}
//导航
function onOpenLocation() {
  onToggleAddresTc(false)
  uni.openLocation({
    latitude: detail.value.latitude,
    longitude: detail.value.longitude,
    success: function () {},
  })
}
// 复制
function onCopy(key) {
  let data = detail.value[key]
  console.log('data', data)
  uni.setClipboardData({
    data: data,
    success: function () {
      uni.showToast({
        title: '复制成功',
        icon: 'none',
      })
    },
  })
}
// 选择快捷问题
function onSelectPrequestion(item = {}) {
  if (loading.value) return
  uni.navigateTo({
    url: `/pages/chat/chat?question=${JSON.stringify(item)}&id=${detail.value.id}`,
  })
}
</script>

<style lang="scss" scoped>
.video-wrap {
  overflow: hidden;
  background-color: #ddd;
}
.video-box {
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 147%;
  &.video {
    z-index: 2;
  }
  &.img {
    z-index: 5;
  }
}
.page-wrap {
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: constant(safe-area-inset-bottom);
}
.bg-content-main {
  padding-top: 20rpx;
}
.page-title-box {
  padding-top: var(--status-bar-height);
  // height: 116rpx;
}
.avatar-box {
  width: 160rpx;
  height: 160rpx;
}
.copyicon-box {
  width: 40rpx;
  height: 40rpx;
}
.bgWrap {
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  overflow: hidden;
  .logo-box {
    width: 220rpx;
    height: 50rpx;
  }
  .share-box {
    z-index: 5;
    color: #fff;
    line-height: 1;
    top: 0;
    right: -26rpx;
    width: 188rpx;
    height: 56rpx;
    padding: 0;
    border-radius: none;
    border: 2rpx solid rgba(#fff, 0.6);
    border-top-left-radius: 80rpx;
    border-bottom-left-radius: 80rpx;
    background-color: rgba(#000, 0.3);
    border-right: none;
    .icon-box {
      width: 24rpx;
      height: 24rpx;
    }
  }
  .more-box {
    width: 12rpx;
    height: 66rpx;
  }
}
.bgContent {
  padding-bottom: 146.4%;
  height: 0;
}
.contact-item {
  min-width: 284rpx;
  max-width: 380rpx;
  height: 140rpx;
  // margin-bottom: 2rpx;
  border-width: 2rpx;
  .icon-box {
    width: 40rpx;
    height: 40rpx;
  }
}
.contact-input {
  $h: 112rpx;
  height: $h;
  width: 100%;
  border-radius: $h;
  .line {
    background-color: #b7bac0;
    width: 4rpx;
    height: 1em;
    margin: 0 20rpx;
  }
}
.address-box {
  width: 100%;
  height: 244rpx;
}
.qrcode-box {
  width: 416rpx;
  height: 416rpx;
}
.loading-box {
  width: 40rpx;
  height: 40rpx;
}
/* 渐变过渡动画 */
.animate__fadeOut {
  animation: animateFadeOut 0.25s 1 linear;
  animation-fill-mode: forwards;
  transition: all 0.25s ease;
}
.animate__fadeIn {
  animation: animateFadeIn 0.5s 1 linear;
  animation-fill-mode: forwards;
  transition: all 0.5s ease;
}
.replay-box {
  width: 72rpx;
  height: 72rpx;
  margin-left: -10rpx;
}
@keyframes animateFadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
@keyframes animateFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
