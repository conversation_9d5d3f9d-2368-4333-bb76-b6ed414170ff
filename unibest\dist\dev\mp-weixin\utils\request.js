"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const store_user = require("../store/user.js");
function http(options) {
  return new Promise((resolve, reject) => {
    common_vendor.index.request(__spreadProps(__spreadValues({}, options), {
      dataType: "json",
      // 响应成功
      success(res) {
        var _a;
        if (res.statusCode >= 200 && res.statusCode < 300) {
          if (options.native) {
            resolve(res);
            return;
          }
          if (((_a = res.data) == null ? void 0 : _a.code) === 401) {
            const userStore = store_user.useUserStore();
            common_vendor.index.showToast({
              icon: "none",
              title: "token过期，正在为你跳转到自动登录",
              duration: 2e3
            });
            userStore.clearStorage();
            common_vendor.index.redirectTo({
              url: "/pages/cardDetail/cardDetail"
            });
            reject(res);
            return;
          }
          resolve(res.data);
        } else if (res.statusCode === 401) {
          const userStore = store_user.useUserStore();
          userStore.clearStorage();
          common_vendor.index.redirectTo({
            url: "/pages/cardDetail/cardDetail"
          });
          reject(res);
        } else {
          !options.hideErrorToast && common_vendor.index.showToast({
            icon: "none",
            title: "请求出错",
            duration: 2e3
          });
          reject(res);
        }
      },
      // 响应失败
      fail(err) {
        common_vendor.index.showToast({
          icon: "none",
          title: "网络错误，换个网络试试"
        });
        reject(err);
      }
    }));
  });
}
http.get = function(url, data, options = {}) {
  return http(__spreadValues({
    url,
    method: "GET",
    data
  }, options));
};
http.post = function(url, data, options = {}) {
  return http(__spreadValues({
    url,
    method: "POST",
    data
  }, options));
};
exports.http = http;
