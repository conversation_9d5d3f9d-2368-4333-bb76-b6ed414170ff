"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../../../../common/vendor.js");
const __default__ = {
  name: "wd-tabbar",
  options: {
    addGlobalClass: true,
    virtualHost: true,
    styleIsolation: "shared"
  }
};
const _sfc_main = /* @__PURE__ */ common_vendor.defineComponent(__spreadProps(__spreadValues({}, __default__), {
  props: common_vendor.tabbarProps,
  emits: ["change", "update:modelValue"],
  setup(__props, { emit: __emit }) {
    const props = __props;
    const emit = __emit;
    const height = common_vendor.ref("");
    const { proxy } = common_vendor.getCurrentInstance();
    const { linkChildren } = common_vendor.useChildren(common_vendor.TABBAR_KEY);
    linkChildren({
      props,
      setChange
    });
    const rootStyle = common_vendor.computed(() => {
      const style = {};
      if (common_vendor.isDef(props.zIndex)) {
        style["z-index"] = props.zIndex;
      }
      return `${common_vendor.objToStyle(style)}${props.customStyle}`;
    });
    common_vendor.watch(
      [() => props.fixed, () => props.placeholder],
      () => {
        setPlaceholderHeight();
      },
      { deep: true, immediate: false }
    );
    common_vendor.onMounted(() => {
      if (props.fixed && props.placeholder) {
        common_vendor.nextTick$1(() => {
          setPlaceholderHeight();
        });
      }
    });
    function setChange(child) {
      let active = child.name;
      emit("update:modelValue", active);
      emit("change", {
        value: active
      });
    }
    function setPlaceholderHeight() {
      if (!props.fixed || !props.placeholder) {
        return;
      }
      common_vendor.getRect(".wd-tabbar", false, proxy).then((res) => {
        height.value = Number(res.height);
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.n(`wd-tabbar wd-tabbar--${_ctx.shape} ${_ctx.customClass} ${_ctx.fixed ? "is-fixed" : ""} ${_ctx.safeAreaInsetBottom ? "is-safe" : ""} ${_ctx.bordered ? "is-border" : ""}`),
        b: common_vendor.s(rootStyle.value),
        c: _ctx.fixed && _ctx.placeholder && _ctx.safeAreaInsetBottom && _ctx.shape === "round" ? 1 : "",
        d: common_vendor.unref(common_vendor.addUnit)(height.value)
      };
    };
  }
}));
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a13e1890"]]);
wx.createComponent(Component);
