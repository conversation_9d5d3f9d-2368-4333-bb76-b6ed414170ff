<view class="data-v-6a12ec76"><wd-popup wx:if="{{w}}" class="data-v-6a12ec76" u-s="{{['d']}}" bindclickModal="{{t}}" u-i="6a12ec76-0" bind:__l="__l" bindupdateModelValue="{{v}}" u-p="{{w}}"><view class="{{['data-v-6a12ec76', s]}}"><view class="{{['data-v-6a12ec76', j]}}"><view wx:if="{{a}}" class="wd-message-box__title data-v-6a12ec76">{{b}}</view><view class="wd-message-box__content data-v-6a12ec76"><block wx:if="{{c}}"><wd-input wx:if="{{f}}" class="data-v-6a12ec76" bindinput="{{d}}" u-i="6a12ec76-1,6a12ec76-0" bind:__l="__l" bindupdateModelValue="{{e}}" u-p="{{f}}"/><view wx:if="{{g}}" class="wd-message-box__input-error data-v-6a12ec76">{{h}}</view></block><block wx:if="{{$slots.d}}"><slot></slot></block><block wx:else>{{i}}</block></view></view><view class="{{['data-v-6a12ec76', r]}}"><wd-button wx:if="{{k}}" class="data-v-6a12ec76" u-s="{{['d']}}" bindclick="{{m}}" u-i="6a12ec76-2,6a12ec76-0" bind:__l="__l" u-p="{{n}}">{{l}}</wd-button><wd-button wx:if="{{q}}" class="data-v-6a12ec76" u-s="{{['d']}}" bindclick="{{p}}" u-i="6a12ec76-3,6a12ec76-0" bind:__l="__l" u-p="{{q}}">{{o}}</wd-button></view></view></wd-popup></view>