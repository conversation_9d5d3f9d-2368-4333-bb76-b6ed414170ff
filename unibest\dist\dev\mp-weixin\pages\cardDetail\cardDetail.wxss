/* stylelint-disable comment-empty-line-before */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
.video-wrap.data-v-d1a3008d {
  overflow: hidden;
  background-color: #ddd;
}
.video-box.data-v-d1a3008d {
  top: 0;
  left: 0;
  position: absolute;
  width: 100%;
  height: 147%;
}
.video-box.video.data-v-d1a3008d {
  z-index: 2;
}
.video-box.img.data-v-d1a3008d {
  z-index: 5;
}
.page-wrap.data-v-d1a3008d {
  padding-bottom: env(safe-area-inset-bottom);
  padding-bottom: constant(safe-area-inset-bottom);
}
.bg-content-main.data-v-d1a3008d {
  padding-top: 20rpx;
}
.page-title-box.data-v-d1a3008d {
  padding-top: var(--status-bar-height);
}
.avatar-box.data-v-d1a3008d {
  width: 160rpx;
  height: 160rpx;
}
.copyicon-box.data-v-d1a3008d {
  width: 40rpx;
  height: 40rpx;
}
.bgWrap.data-v-d1a3008d {
  border-bottom-left-radius: 16rpx;
  border-bottom-right-radius: 16rpx;
  overflow: hidden;
}
.bgWrap .logo-box.data-v-d1a3008d {
  width: 220rpx;
  height: 50rpx;
}
.bgWrap .share-box.data-v-d1a3008d {
  z-index: 5;
  color: #fff;
  line-height: 1;
  top: 0;
  right: -26rpx;
  width: 188rpx;
  height: 56rpx;
  padding: 0;
  border-radius: none;
  border: 2rpx solid rgba(255, 255, 255, 0.6);
  border-top-left-radius: 80rpx;
  border-bottom-left-radius: 80rpx;
  background-color: rgba(0, 0, 0, 0.3);
  border-right: none;
}
.bgWrap .share-box .icon-box.data-v-d1a3008d {
  width: 24rpx;
  height: 24rpx;
}
.bgWrap .more-box.data-v-d1a3008d {
  width: 12rpx;
  height: 66rpx;
}
.bgContent.data-v-d1a3008d {
  padding-bottom: 146.4%;
  height: 0;
}
.contact-item.data-v-d1a3008d {
  min-width: 284rpx;
  max-width: 380rpx;
  height: 140rpx;
  border-width: 2rpx;
}
.contact-item .icon-box.data-v-d1a3008d {
  width: 40rpx;
  height: 40rpx;
}
.contact-input.data-v-d1a3008d {
  height: 112rpx;
  width: 100%;
  border-radius: 112rpx;
}
.contact-input .line.data-v-d1a3008d {
  background-color: #b7bac0;
  width: 4rpx;
  height: 1em;
  margin: 0 20rpx;
}
.address-box.data-v-d1a3008d {
  width: 100%;
  height: 244rpx;
}
.qrcode-box.data-v-d1a3008d {
  width: 416rpx;
  height: 416rpx;
}
.loading-box.data-v-d1a3008d {
  width: 40rpx;
  height: 40rpx;
}

/* 渐变过渡动画 */
.animate__fadeOut.data-v-d1a3008d {
  animation: animateFadeOut-d1a3008d 0.25s 1 linear;
  animation-fill-mode: forwards;
  transition: all 0.25s ease;
}
.animate__fadeIn.data-v-d1a3008d {
  animation: animateFadeIn-d1a3008d 0.5s 1 linear;
  animation-fill-mode: forwards;
  transition: all 0.5s ease;
}
.replay-box.data-v-d1a3008d {
  width: 72rpx;
  height: 72rpx;
  margin-left: -10rpx;
}
@keyframes animateFadeOut-d1a3008d {
0% {
    opacity: 1;
}
100% {
    opacity: 0;
}
}
@keyframes animateFadeIn-d1a3008d {
0% {
    opacity: 0;
}
100% {
    opacity: 1;
}
}