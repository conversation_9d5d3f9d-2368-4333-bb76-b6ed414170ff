<layout-default-uni class="data-v-a041b13f" u-s="{{['d']}}" u-i="a041b13f-0" bind:__l="__l"><view class="height-100vh box-border bg-light page-wrap flex flex-column overflow-y-hidden data-v-a041b13f"><view class="flex-1 position-relative data-v-a041b13f" id="scrollViewWrap"><scroll-view ref="scrollView" id="scrollView" scroll-y="true" class="list-wrap box-border no-scrollbar data-v-a041b13f" style="{{'height:' + e}}" refresher-enabled="{{true}}" scroll-with-animation="{{false}}" show-scrollbar="{{false}}" scroll-anchoring="{{true}}" scroll-top="{{f}}" refresher-triggered="{{g}}" bindrefresherpulling="{{h}}" bindrefresherrefresh="{{i}}" bindrefresherrestor="{{j}}" bindrefresherabort="{{k}}" bindscroll="{{l}}"><view class="px-4 data-v-a041b13f"><view class="card-wrap mb-4 data-v-a041b13f"><view class="card-main position-relative rounded-12 p-base box-border bg-light-detail flex align-center data-v-a041b13f"><view class="logo-box data-v-a041b13f"><image src="{{a}}" mode="aspectFit" class="width-100 height-100 data-v-a041b13f"></image></view><view class="flex-1 text-theme-brand pt-2 data-v-a041b13f"><view class="font-weight-bold font-14 mb-1 data-v-a041b13f">您好，我是{{b}}</view><view class="font-12 data-v-a041b13f">很高兴为您服务，有什么问题尽管问</view></view><view class="flex-shrink flex justify-end data-v-a041b13f"><button open-type="share" class="bg-theme-brand text-white py-1 font-12 flex align-center justify-evenly rounded-8 card-box data-v-a041b13f"><view class="share-box flex algin-center justify-center data-v-a041b13f"><image src="{{c}}" mode="aspectFit" class="width-100 height-100 data-v-a041b13f"></image></view> 名片 </button></view></view></view><view class="flex flex-row flex-wrap pb-base data-v-a041b13f"><block wx:for="{{d}}" wx:for-item="item"><view wx:if="{{item.a}}" class="flex justify-end width-100 list-item-hook data-v-a041b13f"><view class="mb-4 bg-theme-brand text-white font-14 rounded-8 rounded-top-right-0 d-inline-block py-2 px-1-2 data-v-a041b13f">{{item.b}}</view></view><block wx:else><view wx:if="{{item.c}}" class="gif-box mb-4 data-v-a041b13f"><image src="{{item.d}}" mode="aspectFit" class="width-100 height-100 data-v-a041b13f"></image></view><view wx:else class="flex width-100 list-item-hook data-v-a041b13f"><view wx:if="{{item.e}}" class="mb-4 bg-white text-theme-brand font-14 rounded-8 rounded-top-left-0 d-inline-block data-v-a041b13f"><zero-markdown-view wx:if="{{item.g}}" class="data-v-a041b13f" u-i="{{item.f}}" bind:__l="__l" u-p="{{item.g}}"></zero-markdown-view></view></view></block></block></view></view></scroll-view></view><view class="flex-shrink pageFoot box-border p-4 pt-0 data-v-a041b13f" style="{{'margin-bottom:' + C}}"><view wx:if="{{m}}" class="flex justify-center text-second font-12 stop-wrap data-v-a041b13f"> 你中止了本次回答 <text class="mx-0-8 data-v-a041b13f" style="color:#4d5bec" catchtap="{{n}}">重新编辑问题</text></view><view class="scroll-row mb-base box-border overflow-x-auto no-scrollbar data-v-a041b13f"><view wx:for="{{o}}" wx:for-item="item" wx:key="b" bindtap="{{item.c}}" class="{{['rounded-80', 'py-1', 'px-3', 'text-second', 'bg-info', 'font-12', 'mr-base', 'mb', 'scroll-row-item', 'data-v-a041b13f', p]}}">{{item.a}}</view></view><view class="flex align-center justify-evenly mb-base data-v-a041b13f"><view class="flex-shrink btn-box rounded-circle bg-white p-1 flex align-center justify-center data-v-a041b13f"><image src="{{q}}" mode="aspectFit" class="width-100 height-100 data-v-a041b13f"></image></view><view class="flex-1 px-base position-relative data-v-a041b13f"><input type="text" disabled="{{r}}" cursor-spacing="{{20}}" placeholder="请输入您的问题" class="{{['input', 'px-3', 'data-v-a041b13f', s]}}" placeholder-class="text-placeholder font-14" bindconfirm="{{t}}" value="{{v}}" bindinput="{{w}}"/></view><block class="data-v-a041b13f" u-s="{{['d']}}"><view wx:if="{{x}}" class="flex-shrink btn-box rounded-circle bg-white p-1 flex align-center justify-center data-v-a041b13f" hover-class="bg-hover" bindtap="{{z}}"><image src="{{y}}" mode="aspectFit" class="width-100 height-100 data-v-a041b13f"></image></view><view wx:else class="flex-shrink btn-box rounded-circle bg-white p-1 flex align-center justify-center data-v-a041b13f" hover-class="bg-hover" catchtap="{{B}}"><image src="{{A}}" mode="aspectFit" class="width-100 height-100 data-v-a041b13f"></image></view></block></view></view></view></layout-default-uni>