"use strict";
const common_vendor = require("../../common/vendor.js");
const mockCard = require("../../mock-card.js");
if (!Array) {
  const _easycom_wd_button2 = common_vendor.resolveComponent("wd-button");
  const _easycom_wd_input2 = common_vendor.resolveComponent("wd-input");
  const _easycom_wd_tag2 = common_vendor.resolveComponent("wd-tag");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_easycom_wd_button2 + _easycom_wd_input2 + _easycom_wd_tag2 + _component_layout_default_uni)();
}
const _easycom_wd_button = () => "../../node-modules/wot-design-uni/components/wd-button/wd-button.js";
const _easycom_wd_input = () => "../../node-modules/wot-design-uni/components/wd-input/wd-input.js";
const _easycom_wd_tag = () => "../../node-modules/wot-design-uni/components/wd-tag/wd-tag.js";
if (!Math) {
  (_easycom_wd_button + _easycom_wd_input + _easycom_wd_tag)();
}
const _sfc_main = {
  __name: "index",
  setup(__props) {
    const list = common_vendor.ref(mockCard.mockJson);
    const searchText = common_vendor.ref("");
    const filteredList = common_vendor.computed(() => {
      if (!searchText.value) {
        return list.value;
      }
      return list.value.filter(
        (item) => item.name && item.name.includes(searchText.value) || item.title && item.title.includes(searchText.value)
      );
    });
    function onOpencard(item) {
      common_vendor.index.navigateTo({
        url: `/pages/cardDetail/cardDetail?id=${item.id}`
      });
    }
    return (_ctx, _cache) => {
      return {
        a: common_vendor.p({
          type: "primary"
        }),
        b: common_vendor.p({
          type: "success"
        }),
        c: common_vendor.p({
          type: "warning"
        }),
        d: common_vendor.o(($event) => searchText.value = $event),
        e: common_vendor.p({
          placeholder: "搜索名片...",
          modelValue: searchText.value
        }),
        f: common_vendor.p({
          type: "primary"
        }),
        g: common_vendor.p({
          type: "success"
        }),
        h: common_vendor.p({
          type: "warning"
        }),
        i: common_vendor.f(filteredList.value, (item, index, i0) => {
          return {
            a: item.picture,
            b: common_vendor.t(item.name || "名片"),
            c: index,
            d: common_vendor.o(($event) => onOpencard(item), index)
          };
        })
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-83a5a03c"]]);
wx.createPage(MiniProgramPage);
