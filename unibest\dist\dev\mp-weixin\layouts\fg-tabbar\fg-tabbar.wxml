<wd-tabbar wx:if="{{a}}" u-s="{{['d']}}" bindchange="{{c}}" u-i="18de3e18-0" bind:__l="__l" bindupdateModelValue="{{d}}" u-p="{{e}}"><block wx:for="{{b}}" wx:for-item="item" wx:key="m"><wd-tabbar-item wx:if="{{item.a}}" u-i="{{item.b}}" bind:__l="__l" u-p="{{item.c}}"/><wd-tabbar-item wx:elif="{{item.d}}" u-s="{{['icon']}}" u-i="{{item.g}}" bind:__l="__l" u-p="{{item.h}}"><view h-40rpx w-40rpx class="{{[item.e, item.f]}}" slot="icon"/></wd-tabbar-item><wd-tabbar-item wx:elif="{{item.i}}" u-s="{{['icon']}}" u-i="{{item.k}}" bind:__l="__l" u-p="{{item.l}}"><image src="{{item.j}}" h-40rpx w-40rpx slot="icon"/></wd-tabbar-item></block></wd-tabbar>