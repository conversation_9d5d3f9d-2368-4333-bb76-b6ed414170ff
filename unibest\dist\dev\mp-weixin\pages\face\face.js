"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
const _sfc_main = {
  __name: "face",
  setup(__props) {
    const avatar = common_vendor.ref("");
    common_vendor.ref("");
    const imgOriginInfo = common_vendor.reactive({
      width: 100,
      height: 100
    });
    const imageUrl = common_vendor.ref("");
    common_vendor.onShareAppMessage((e) => {
      let shareInfo = {
        path: `/pages/index/index?id=10`,
        title: `张三`,
        imageUrl: avatar.value
      };
      return shareInfo;
    });
    common_vendor.onReady(() => {
    });
    function chooseAndProcessImage() {
      common_vendor.wx$1.chooseMedia({
        count: 1,
        mediaType: ["image"],
        success: (res) => {
          console.log("chooseMedia res", res);
          const imgUrl = res.tempFiles[0].tempFilePath;
          imageUrl.value = imgUrl;
          formatFaceData(imgUrl);
        }
      });
    }
    function formatFaceData(imagePath = "") {
      return __async(this, null, function* () {
        common_vendor.index.getImageInfo({
          src: imagePath,
          success: (res) => {
            const { width, height } = res;
            console.log("getImageInfo res", res);
            imgOriginInfo.width = width;
            imgOriginInfo.height = height;
            _session(imagePath);
          },
          fail: (res) => {
            console.error("getImageInfo:err", res);
          }
        });
      });
    }
    function _session(imagePath = "") {
      return __async(this, null, function* () {
        const session = common_vendor.index.createVKSession({
          track: {
            face: { mode: 2 }
            // mode: 1 - 使用摄像头；2 - 手动传入图像
          }
        });
        const canvas = common_vendor.wx$1.createOffscreenCanvas({
          type: "2d",
          width: imgOriginInfo.width,
          height: imgOriginInfo.height
        });
        const ctx = canvas.getContext("2d");
        canvas.width = imgOriginInfo.width;
        canvas.height = imgOriginInfo.height;
        const img = canvas.createImage();
        yield new Promise((resolve, reject) => {
          img.onload = resolve;
          img.src = imagePath;
          img.onerror = reject;
        });
        ctx.clearRect(0, 0, imgOriginInfo.width, imgOriginInfo.height);
        ctx.drawImage(img, 0, 0, imgOriginInfo.width, imgOriginInfo.height);
        common_vendor.index.canvasToTempFilePath({
          canvas,
          success(res) {
            avatar.value = res.tempFilePath;
          }
        });
        const pixelData = ctx.getImageData(0, 0, imgOriginInfo.width, imgOriginInfo.height).data;
        const arrayBuffer = pixelData.buffer;
        console.log("arrayBuffer", arrayBuffer);
        session.on("updateAnchors", (anchors) => {
          console.log("updateAnchors", anchors);
        });
        session.start((errno) => {
          if (errno) {
            console.log("errno", errno);
          } else {
            const result = session.detectFace({
              frameBuffer: arrayBuffer,
              width: imgOriginInfo.width,
              height: imgOriginInfo.height,
              scoreThreshold: 0.5,
              sourceType: 1,
              modelMode: 1
            });
            session.on("addAnchors", (anchors) => {
              console.log("addAnchors", anchors);
            });
            console.log("成功", session.detectBody, result);
          }
        });
      });
    }
    function onCreateAvatar() {
      const query = common_vendor.wx$1.createSelectorQuery();
      query.select("#myCanvas").fields({ node: true, size: true }).exec((res) => {
        const canvas = res[0].node;
        const ctx = canvas.getContext("2d");
        const dpr = common_vendor.wx$1.getSystemInfoSync().pixelRatio;
        const width = res[0].width;
        const height = res[0].height;
        console.log();
        canvas.width = width * dpr;
        canvas.height = height * dpr;
        ctx.scale(dpr, dpr);
        const bgImage = canvas.createImage();
        const img2 = canvas.createImage();
        const avatarImg = canvas.createImage();
        ctx.imageSmoothingEnabled = true;
        bgImage.src = "/static/images/<EMAIL>";
        bgImage.crossOrigin = "anonymous";
        bgImage.onerror = function() {
          console.log("图片加载失败，请尝试其他图片");
        };
        bgImage.onload = (e) => {
          ctx.clearRect(0, 0, canvas.width, canvas.height);
          ctx.drawImage(bgImage, 0, 0, width, height);
          avatarImg.src = "/static/images/image.png";
          avatarImg.crossOrigin = "anonymous";
          const avatarImgDx = width - 30 - 182;
          const avatarImgDy = 24;
          avatarImg.onload = (e2) => {
            ctx.drawImage(bgImage, 0, 0, width, height);
            ctx.drawImage(avatarImg, avatarImgDx, avatarImgDy, 182, 284);
            img2.src = "/static/images/<EMAIL>";
            img2.crossOrigin = "anonymous";
            const img2H = width * 107 / 500;
            const img2dy = height - img2H;
            img2.onload = (e3) => {
              ctx.drawImage(img2, 0, img2dy, width, img2H);
              drawText(ctx);
              common_vendor.index.canvasToTempFilePath({
                canvas,
                success(res2) {
                  console.log("res", res2);
                  avatar.value = res2.tempFilePath;
                }
              });
            };
          };
        };
      });
      function drawText(ctx) {
        ctx.font = `bold 36px 苹方-简 中粗体`;
        ctx.fillStyle = "#092119";
        ctx.textAlign = "left";
        ctx.textBaseline = "middle";
        ctx.fillText("林薇", 53, 106);
        ctx.font = `18px 苹方-简 常规体`;
        ctx.fillStyle = "#092119";
        ctx.textAlign = "left";
        ctx.fillText("EaseInsur", 53, 160);
        ctx.fillText("高级销售经理", 53, 195);
      }
    }
    return (_ctx, _cache) => {
      return {
        a: imgOriginInfo.width + "px",
        b: imgOriginInfo.height,
        c: common_vendor.o(onCreateAvatar),
        d: common_vendor.o(chooseAndProcessImage),
        e: avatar.value,
        f: imageUrl.value
      };
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-2a8dfbb1"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
