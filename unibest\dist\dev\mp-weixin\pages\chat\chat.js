"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_config = require("../../api/config.js");
const store_user = require("../../store/user.js");
const utils_request = require("../../utils/request.js");
const mockCard = require("../../mock-card.js");
if (!Array) {
  const _component_zero_markdown_view = common_vendor.resolveComponent("zero-markdown-view");
  const _component_template = common_vendor.resolveComponent("template");
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  (_component_zero_markdown_view + _component_template + _component_layout_default_uni)();
}
const PAGE_SIZE = 10;
const _sfc_main = {
  __name: "chat",
  setup(__props) {
    const userStore = store_user.useUserStore();
    const socketTask = common_vendor.ref(null);
    common_vendor.ref(null);
    const cardId = common_vendor.ref("");
    const detail = common_vendor.ref({
      skills: []
    });
    const activePrequestionIndex = common_vendor.ref(-1);
    const agentInfo = common_vendor.ref({});
    const nowSocketId = common_vendor.ref(0);
    const isReply = common_vendor.ref(false);
    const isStop = common_vendor.ref(false);
    const socketReceiveNum = common_vendor.ref(0);
    const connectOk = common_vendor.ref(false);
    const scrollTop = common_vendor.ref(0);
    const scrollType = common_vendor.ref("lower");
    const scrollViewHeight = common_vendor.ref(400);
    const userMessage = common_vendor.ref("");
    const systemInfo = common_vendor.reactive({
      osName: "",
      safeAreaInsets: {}
    });
    const chatList = common_vendor.ref([]);
    const refresherTriggered = common_vendor.ref(false);
    const pageNum = common_vendor.ref(1);
    const total = common_vendor.ref(0);
    const loading = common_vendor.ref(false);
    common_vendor.watch([connectOk, agentInfo], (e) => {
      if (e[0] && e[1].id) {
        if (activePrequestionIndex.value != -1) {
          const q = detail.value.skills[activePrequestionIndex.value].content;
          _completions(q);
        }
      }
    });
    common_vendor.onReady(() => {
      const scrollViewWrap = common_vendor.index.createSelectorQuery().select("#scrollViewWrap");
      scrollViewWrap.boundingClientRect((res) => {
        if (res) {
          scrollViewHeight.value = res.height;
        }
      }).exec();
    });
    common_vendor.onLoad((e) => {
      if (e.id) {
        cardId.value = e.id;
        detail.value = mockCard.mockJson[cardId.value];
        if (e.question) {
          const question = JSON.parse(e.question);
          const index = detail.value.skills.findIndex((item) => item.id == question.id);
          activePrequestionIndex.value = index;
          chatList.value.push(question);
        }
      }
      common_vendor.index.getSystemInfo({
        success: function(res) {
          systemInfo.osName = res.osName;
          systemInfo.safeAreaInsets = res.safeAreaInsets;
        }
      });
      initPage();
    });
    common_vendor.onUnload(() => {
      onStop("正在退出对话");
      socketTask.value.close();
    });
    common_vendor.onUnmounted(() => {
      socketTask.value.close();
    });
    common_vendor.onShareAppMessage((e) => {
      let shareInfo = {
        path: `/pages/cardDetail/cardDetail?id=${detail.value.id}`,
        title: `${detail.value.name} - ${detail.value.company}｜${detail.value.job}`,
        imageUrl: detail.value.sharePic
      };
      return shareInfo;
    });
    function onScroll(e) {
      const detail2 = e.detail;
      const h = Math.ceil(detail2.scrollHeight);
      switch (scrollType.value) {
        case "lower":
          if (socketReceiveNum.value == 0) {
            scrollType.value = "";
            if (scrollTop.value != h) {
              scrollTop.value = h;
            }
          } else {
            const a = Math.abs(scrollTop.value - h);
            if (a > 200) {
              scrollTop.value = h;
            }
          }
          break;
      }
    }
    function onRefresherpulling() {
      refresherTriggered.value = true;
    }
    function onRefresherrestor() {
      refresherTriggered.value = false;
    }
    function onRefresherabort() {
      refresherTriggered.value = false;
    }
    function onScrolltoupper() {
      if (loading.value) {
        return;
      }
      const pages = Math.ceil(total.value / PAGE_SIZE);
      scrollType.value = "none";
      if (pageNum.value < pages) {
        pageNum.value++;
        _record((data = []) => {
          const d = data.slice().reverse();
          chatList.value = [...d, ...chatList.value];
        });
      } else {
        loading.value = false;
        refresherTriggered.value = false;
      }
    }
    function initPage() {
      return __async(this, null, function* () {
        var _a;
        socketTask.value = common_vendor.index.connectSocket({
          url: `wss://${api_config.sys.ip}/${api_config.apiMap.conversation.websocket}`,
          header: {
            Authorization: `Bearer ${userStore.accessToken}`,
            Clientid: (_a = userStore == null ? void 0 : userStore.loginRes) == null ? void 0 : _a.client_id
          },
          success: (res) => {
            connectOk.value = true;
          },
          fail: (err) => {
            connectOk.value = false;
            console.log("connectSocket：err", err);
            common_vendor.index.showToast({
              title: `网络异常：${err}`,
              icon: "none",
              duration: 2e3
            });
          },
          complete: () => {
          }
        });
        socketTask.value.onOpen((e) => {
          console.log("socket:打开成功");
          socketTask.value.onMessage((e2) => {
            const d = JSON.parse(e2.data);
            const data = __spreadProps(__spreadValues({}, d), { role: "assistant" });
            const clen = chatList.value.length;
            const index = chatList.value.findIndex((item, index2) => {
              return item.id == data.id;
            });
            nowSocketId.value = data.id;
            if (!data.end) {
              socketReceiveNum.value++;
              if (!isStop.value) {
                isReply.value = true;
              }
              if (index < 0) {
                chatList.value[clen - 1] = __spreadProps(__spreadValues({}, data), { role: "loading" });
              } else {
                if (data.content) {
                  const c = chatList.value[clen - 1].content + data.content;
                  chatList.value[clen - 1]["content"] = c;
                  chatList.value[clen - 1].role = data.role;
                }
              }
            } else {
              if (index < 0) {
                chatList.value[clen - 1] = data;
              }
              chatList.value[clen - 1].role = data.role;
              socketReceiveNum.value = 0;
              isReply.value = false;
            }
            common_vendor.nextTick$1(() => {
              _scrollToBottom("add");
            });
          });
        });
        socketTask.value.onClose((e) => {
          socketReceiveNum.value = 0;
          isReply.value = false;
          console.log("socket:关闭成功");
        });
        socketTask.value.onError((e) => {
          socketReceiveNum.value = 0;
          isReply.value = false;
          console.log("socket:链接出错", e.errMsg);
        });
        yield _getOrCreateByAgent();
        _record((data = []) => {
          const d = data.slice().reverse();
          if (chatList.value.length > 0) {
            chatList.value = [...d, ...chatList.value];
          } else {
            chatList.value = d;
          }
          common_vendor.nextTick$1(() => {
            _scrollToBottom("record");
          });
        });
      });
    }
    function _completions(question = "") {
      return __async(this, null, function* () {
        const msg = userMessage.value;
        userMessage.value = "";
        _chatloading();
        const reqdata = {
          conversationId: agentInfo.value.id,
          userMessage: question || msg,
          hasImages: false
        };
        const res = yield utils_request.http.post(api_config.apiMap.conversation.completions, reqdata, { isJson: true });
        if (res.code === 200) {
          userMessage.value = "";
        } else {
          common_vendor.index.showToast({
            title: res.msg,
            icon: "none",
            duration: 2e3
          });
        }
      });
    }
    function _record(callback = () => {
    }) {
      return __async(this, null, function* () {
        loading.value = true;
        const reqdata = {
          conversationId: agentInfo.value.id,
          pageNum: pageNum.value,
          pageSize: PAGE_SIZE,
          orderByColumn: "id",
          isAsc: "desc"
        };
        const res = yield utils_request.http.get(api_config.apiMap.conversation.record, reqdata, { isJson: true });
        loading.value = false;
        refresherTriggered.value = false;
        if (res.code === 200) {
          total.value = res.total;
          if (typeof callback === "function") {
            callback(res.rows);
          }
        } else {
          common_vendor.index.showToast({
            title: res.msg,
            icon: "none",
            duration: 2e3
          });
        }
        return res;
      });
    }
    function _getOrCreateByAgent() {
      return __async(this, null, function* () {
        common_vendor.index.showLoading({
          mask: true
        });
        const reqdata = {
          agentId: detail.value.agentId,
          outwardType: "WEB"
        };
        const res = yield utils_request.http.get(api_config.apiMap.conversation.getOrCreateByAgent, reqdata, { isJson: true });
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          agentInfo.value = res.data;
        } else {
          common_vendor.index.showToast({
            title: res.msg,
            icon: "none",
            duration: 2e3
          });
        }
        return res;
      });
    }
    function _scrollToBottom(type = "record") {
      scrollType.value = "lower";
      const query = common_vendor.index.createSelectorQuery();
      query.selectAll("#scrollView").boundingClientRect();
      query.selectAll(".list-item-hook").boundingClientRect();
      query.exec((res) => {
        const scrollView = res[0][0];
        const items = res[1];
        if (type == "record") {
          if (items && items.length > 0) {
            const len = items.length;
            const lastItem = items[len - 1];
            scrollTop.value = lastItem.bottom;
          }
        }
        if (type == "add") {
          if (scrollTop.value >= scrollView.height) {
            scrollTop.value += 30;
          }
        }
      });
    }
    function onSelectPrequestion(item = {}) {
      if (!connectOk.value)
        return;
      if (isReply.value)
        return;
      chatList.value.push(item);
      isStop.value = false;
      common_vendor.nextTick$1(() => {
        _scrollToBottom("add");
      });
      _completions(item.content);
    }
    function onSend() {
      if (!connectOk.value)
        return;
      if (isReply.value)
        return;
      const _content = userMessage.value.trim();
      if (_content) {
        const obj = {
          content: _content,
          role: "user"
        };
        chatList.value.push(obj);
        isStop.value = false;
        common_vendor.nextTick$1(() => {
          _scrollToBottom("add");
        });
        _completions();
      }
    }
    function onStop(title = "正在中止...") {
      return __async(this, null, function* () {
        const len = chatList.value.length;
        common_vendor.index.showLoading({
          mask: true,
          title
        });
        const res = yield utils_request.http.post(api_config.apiMap.conversation.close + "/" + nowSocketId.value);
        common_vendor.index.hideLoading();
        if (res.code === 200) {
          isReply.value = false;
          isStop.value = true;
          if (chatList.value[len - 1].role == "loading") {
            chatList.value[len - 1].content = "(用户已终止对话)";
            chatList.value[len - 1].role = "assistant";
            common_vendor.nextTick$1(() => {
              _scrollToBottom("add");
            });
          }
        } else {
          common_vendor.index.showToast({
            title: res.msg,
            icon: "none",
            duration: 2e3
          });
        }
      });
    }
    function onReAsk() {
      isReply.value = false;
      isStop.value = false;
    }
    function _chatloading() {
      const obj = {
        id: 0,
        content: "",
        role: "loading"
      };
      chatList.value.push(obj);
      isReply.value = true;
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$2,
        b: common_vendor.t(detail.value.name),
        c: common_assets._imports_1$2,
        d: common_vendor.f(chatList.value, (item, inex, i0) => {
          return common_vendor.e({
            a: item.role == "user"
          }, item.role == "user" ? {
            b: common_vendor.t(item.content)
          } : common_vendor.e({
            c: item.role == "loading"
          }, item.role == "loading" ? {
            d: common_assets._imports_2
          } : common_vendor.e({
            e: item.content
          }, item.content ? {
            f: "a041b13f-1-" + i0 + ",a041b13f-0",
            g: common_vendor.p({
              markdown: item.content
            })
          } : {})));
        }),
        e: isStop.value ? `${scrollViewHeight.value - 30}px` : `${scrollViewHeight.value}px`,
        f: scrollTop.value,
        g: refresherTriggered.value,
        h: common_vendor.o(onRefresherpulling),
        i: common_vendor.o(onScrolltoupper),
        j: common_vendor.o(onRefresherrestor),
        k: common_vendor.o(onRefresherabort),
        l: common_vendor.o(onScroll),
        m: isStop.value
      }, isStop.value ? {
        n: common_vendor.o(onReAsk)
      } : {}, {
        o: common_vendor.f(detail.value.skills, (item, index, i0) => {
          return {
            a: common_vendor.t(item.content),
            b: index,
            c: common_vendor.o(($event) => onSelectPrequestion(item), index)
          };
        }),
        p: common_vendor.n(isReply.value ? "opacity-0_5" : ""),
        q: common_assets._imports_3$1,
        r: isReply.value,
        s: common_vendor.n(isReply.value ? "disabled" : ""),
        t: common_vendor.o(onSend),
        v: userMessage.value,
        w: common_vendor.o(($event) => userMessage.value = $event.detail.value),
        x: !isReply.value
      }, !isReply.value ? {
        y: common_assets._imports_4$1,
        z: common_vendor.o(onSend)
      } : {
        A: common_assets._imports_5$1,
        B: common_vendor.o(onStop)
      }, {
        C: systemInfo.safeAreaInsets.bottom + "px"
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-a041b13f"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
