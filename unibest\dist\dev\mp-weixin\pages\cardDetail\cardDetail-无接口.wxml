<layout-default-uni class="data-v-6209742a" u-s="{{['d']}}" u-i="6209742a-0" bind:__l="__l"><view class="height-100vh flex flex-column box-border page-wrap data-v-6209742a"><view class="position-relative bgWrap zindex-5 flex-1 flex data-v-6209742a"><view class="position-relative bg-content flex-1 width-100 data-v-6209742a"><view class="position-absolute left-0 top-0 right-0 bottom-0 video-wrap data-v-6209742a"><view class="{{['video-box', 'img', 'data-v-6209742a', b]}}"><image src="{{a}}" lazy-load="false" mode="aspectFill" class="width-100 height-100 data-v-6209742a"></image></view><view class="video-box video data-v-6209742a"><video id="myVideo" src="{{c}}" autoplay enable-play-gesture object-fit="cover" poster="{{d}}" class="width-100 height-100 data-v-6209742a" bindplay="{{e}}" bindended="{{f}}" bindpause="{{g}}" bindloadedmetadata="{{h}}"></video></view></view></view><view class="position-absolute left-0 top-0 right-0 bottom-0 zindex-10 text-white flex flex-column bg-content-main data-v-6209742a"><view class="page-title-box flex-shrink text-center flex align-center justify-center font-weight-500 font-m data-v-6209742a">我的名片</view><view class="p-base flex-1 flex flex-column pt-5 data-v-6209742a"><view class="flex justify-between flex-shrink data-v-6209742a"><view class="logo-box mr-base data-v-6209742a"><image src="{{i}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a"></image></view></view><view class="flex-1 flex flex-column justify-end data-v-6209742a"><view wx:if="{{j}}" class="replay-box mb-2 data-v-6209742a" hover-class="bg-hover" bindtap="{{l}}"><image src="{{k}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a"></image></view></view><view class="mb-auto flex-shrink flex align-center position-relative data-v-6209742a"><view class="more-box mr-4 data-v-6209742a"><image src="{{m}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a"></image></view><view class="data-v-6209742a"><view class="font-weight-bold font-20 mb data-v-6209742a">Lisa</view><view class="flex align-center font-14 data-v-6209742a"> EaseGYM健身中心 <label class="mx-1 data-v-6209742a">|</label> 金牌教练 </view></view><button open-type="share" class="share-box position-absolute flex align-center justify-center font-12 data-v-6209742a"><view class="icon-box mr-0-8 flex algin-center data-v-6209742a"><image src="{{n}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a"></image></view> 分享名片 </button></view></view></view></view><view class="p-4 width-100 box-border flex-shrink data-v-6209742a"><view class="scroll-row pb-4 overflow-x-auto no-scrollbar box-border data-v-6209742a"><view wx:for="{{o}}" wx:for-item="item" wx:key="e" class="{{['d-inline-block', 'data-v-6209742a', item.f]}}" bindtap="{{item.g}}"><view class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item data-v-6209742a"><view class="flex align-center mb-1 data-v-6209742a"><view class="icon-box mr-0-8 data-v-6209742a"><image src="{{item.a}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a"></image></view><view class="text-theme-brand font-14 data-v-6209742a">{{item.b}}</view></view><view class="text-third width-100 text-ellipsis font-12 data-v-6209742a"><text wx:if="{{item.c}}" class="mr-1 data-v-6209742a">+86</text> {{item.d}}</view></view></view></view><view class="text-third mb-4 font-12 data-v-6209742a"> ACE-CPT国际认证教练 + NASM运动营养师，助200+人实现体脂降25%、肌肉增15%，专精增肌塑形+损伤重建，王牌课程3D蜜桃臀 & 代谢轰炸舱，用专业助您高效蜕变！ </view><view class="scroll-row mb-4 overflow-x-auto no-scrollbar data-v-6209742a"><view wx:for="{{p}}" wx:for-item="item" wx:key="b" class="{{['rounded-80', 'py-1', 'px-3', 'text-second', 'bg-info', 'font-12', 'mr-base', 'mb', 'scroll-row-item', 'data-v-6209742a', q]}}" bindtap="{{item.c}}">{{item.a}}</view></view><view class="bg-theme-brand text-white text-center normal-button-rounded flex align-center justify-center font-14 data-v-6209742a" style="{{'margin-bottom:' + v}}" bindtap="{{w}}"><view wx:if="{{r}}" class="loading-box data-v-6209742a"><image src="{{s}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a"></image></view><view class="{{['flex', 'align-center', 'data-v-6209742a', t]}}"><text class="mx-0-8 data-v-6209742a">AI</text> 聊一聊 </view></view></view><my-modal wx:if="{{x}}" class="data-v-6209742a" u-s="{{['d']}}" bindclose="{{B}}" u-i="6209742a-1,6209742a-0" bind:__l="__l" u-p="{{C}}"><view class="my-4 bg-input contact-input text-first font-weight-bold font-18 flex align-center justify-center data-v-6209742a"><text class="data-v-6209742a">+86</text><view class="line data-v-6209742a"></view><text class="data-v-6209742a">{{y}}</text></view><view class="flex align-center justify-between data-v-6209742a"><view class="normal-button-rounded text-theme-brand border-current flex-1 font-14 data-v-6209742a" bindtap="{{z}}">复制号码</view><view class="mx-2 flex-shrink data-v-6209742a"></view><view class="normal-button-rounded bg-theme-brand text-white flex-1 font-14 data-v-6209742a" bindtap="{{A}}">拨打号码</view></view></my-modal><my-modal wx:if="{{D}}" class="data-v-6209742a" u-s="{{['titleLeft','d']}}" bindclose="{{J}}" u-i="6209742a-2,6209742a-0" bind:__l="__l" u-p="{{K}}"><view class="flex align-center font-14 data-v-6209742a" slot="titleLeft"><view class="text-<strong></strong>second mr-base data-v-6209742a">微信号</view><view class="text-first font-weight-bold mr-base data-v-6209742a">{{E}}</view><view class="copyicon-box mr-1 data-v-6209742a" bindtap="{{G}}"><image src="{{F}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a"></image></view></view><view class="pt-4 data-v-6209742a"><view class="flex align-center mb-4 data-v-6209742a"><view class="avatar-box mr-4 flex-shrink data-v-6209742a"><image src="{{H}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a"></image></view><view class="flex-1 data-v-6209742a"><view class="font-weight-bold text-first font-20 my-0-8 data-v-6209742a">Lisa</view><view class="text-second font-14 data-v-6209742a">浙江省嘉兴市，浙江省嘉兴市，浙江省嘉兴市</view></view></view><view class="rounded-8 bg-info p-4 data-v-6209742a"><view class="rounded-8 bg-white p-4 mb-4 flex align-center data-v-6209742a"><view class="qrcode-box mx-auto data-v-6209742a"><image src="{{I}}" mode="aspectFit" class="width-100 height-100 data-v-6209742a" show-menu-by-longpress></image></view></view><view class="text-placeholder font-12 text-center data-v-6209742a">扫描二维码，添加我的企业微信</view></view></view></my-modal><my-modal wx:if="{{L}}" class="data-v-6209742a" u-s="{{['d']}}" bindclose="{{O}}" u-i="6209742a-3,6209742a-0" bind:__l="__l" u-p="{{P}}"><view class="my-4 bg-input contact-input text-first font-weight-bold font-18 flex align-center justify-center data-v-6209742a"><text class="data-v-6209742a">{{M}}</text></view><view class="flex align-center justify-center data-v-6209742a"><view class="normal-button-rounded text-theme-brand border-current width-100 font-14 data-v-6209742a" bindtap="{{N}}">复制邮箱</view></view></my-modal><my-modal wx:if="{{Q}}" class="data-v-6209742a" u-s="{{['d']}}" bindclose="{{Y}}" u-i="6209742a-4,6209742a-0" bind:__l="__l" u-p="{{Z}}"><view class="my-4 bg-light-detail rounded-8 overflow-hidden data-v-6209742a"><view class="p-1-2 data-v-6209742a"><view class="text-first font-16 font-weight-bold mb-0-8 data-v-6209742a">{{R}}</view><view class="text-second font-14 data-v-6209742a">{{S}}</view></view><view class="address-box data-v-6209742a"><map class="data-v-6209742a" style="width:100%;height:100%" latitude="{{T}}" longitude="{{U}}" markers="{{V}}"></map></view></view><view class="flex align-center justify-between data-v-6209742a"><view class="normal-button-rounded text-theme-brand border-current flex-1 font-14 data-v-6209742a" bindtap="{{W}}">复制地址</view><view class="mx-2 flex-shrink data-v-6209742a"></view><view class="normal-button-rounded bg-theme-brand text-white flex-1 font-14 data-v-6209742a" bindtap="{{X}}">前端导航</view></view></my-modal></view></layout-default-uni>