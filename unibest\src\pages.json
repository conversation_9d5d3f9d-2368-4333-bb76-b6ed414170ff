{"globalStyle": {"navigationBarTextStyle": "black", "navigationBarTitleText": "EaseGYM", "navigationBarBackgroundColor": "#F8F8F8", "backgroundColor": "#F8F8F8"}, "easycom": {"autoscan": true, "custom": {"^fg-(.*)": "@/components/fg-$1/fg-$1.vue", "^wd-(.*)": "wot-design-uni/components/wd-$1/wd-$1.vue", "^(?!z-paging-refresh|z-paging-load-more)z-paging(.*)": "z-paging/components/z-paging$1/z-paging$1.vue"}}, "pages": [{"path": "pages/face/face", "style": {"navigationBarTitleText": "名片列表"}}, {"path": "pages/index/index", "style": {"navigationBarTitleText": "名片列表"}}, {"path": "pages/cardDetail/cardDetail", "style": {"navigationBarTitleText": "我的名片", "navigationStyle": "custom"}}, {"path": "pages/chat/chat", "style": {"navigationBarTitleText": "我的名片"}}], "condition": {"current": 0, "list": [{"name": "chat", "path": "pages/chat/chat"}]}, "uniIdRouter": {}}