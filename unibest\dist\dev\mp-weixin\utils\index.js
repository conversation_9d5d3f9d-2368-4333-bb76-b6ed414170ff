"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
const common_vendor = require("../common/vendor.js");
const utils_platform = require("./platform.js");
const pages = [
  {
    path: "pages/index/index",
    type: "home"
  },
  {
    path: "pages/about/about",
    type: "page",
    layout: "tabbar",
    style: {
      navigationBarTitleText: "关于"
    }
  },
  {
    path: "pages/about/alova",
    type: "page",
    layout: "default",
    style: {
      navigationBarTitleText: "Alova 请求演示"
    }
  },
  {
    path: "pages/cardDetail/cardDetail-无接口",
    type: "page"
  },
  {
    path: "pages/cardDetail/cardDetail",
    type: "page"
  },
  {
    path: "pages/chat/chat",
    type: "page"
  },
  {
    path: "pages/face/face",
    type: "page"
  }
];
const subPackages = [
  {
    root: "pages-sub",
    pages: [
      {
        path: "demo/index",
        type: "page",
        layout: "default",
        style: {
          navigationBarTitleText: "分包页面"
        }
      }
    ]
  }
];
function getLastPage() {
  const pages2 = getCurrentPages();
  return pages2[pages2.length - 1];
}
function getAllPages(key = "needLogin") {
  const mainPages = pages.filter((page) => !key || page[key]).map((page) => __spreadProps(__spreadValues({}, page), {
    path: `/${page.path}`
  }));
  const subPages = [];
  subPackages.forEach((subPageObj) => {
    const { root } = subPageObj;
    subPageObj.pages.filter((page) => !key || page[key]).forEach((page) => {
      subPages.push(__spreadProps(__spreadValues({}, page), {
        path: `/${root}/${page.path}`
      }));
    });
  });
  const result = [...mainPages, ...subPages];
  return result;
}
const getNeedLoginPages = () => getAllPages("needLogin").map((page) => page.path);
getAllPages("needLogin").map((page) => page.path);
function getEnvBaseUrl() {
  let baseUrl = "https://ukw0y1.laf.run";
  if (utils_platform.isMpWeixin) {
    const {
      miniProgram: { envVersion }
    } = common_vendor.index.getAccountInfoSync();
    switch (envVersion) {
      case "develop":
        baseUrl = "https://ukw0y1.laf.run";
        break;
      case "trial":
        baseUrl = "https://ukw0y1.laf.run";
        break;
      case "release":
        baseUrl = "https://ukw0y1.laf.run";
        break;
    }
  }
  return baseUrl;
}
function getEnvBaseUploadUrl() {
  let baseUploadUrl = "https://ukw0y1.laf.run/upload";
  if (utils_platform.isMpWeixin) {
    const {
      miniProgram: { envVersion }
    } = common_vendor.index.getAccountInfoSync();
    switch (envVersion) {
      case "develop":
        baseUploadUrl = "https://ukw0y1.laf.run/upload";
        break;
      case "trial":
        baseUploadUrl = "https://ukw0y1.laf.run/upload";
        break;
      case "release":
        baseUploadUrl = "https://ukw0y1.laf.run/upload";
        break;
    }
  }
  return baseUploadUrl;
}
exports.getEnvBaseUploadUrl = getEnvBaseUploadUrl;
exports.getEnvBaseUrl = getEnvBaseUrl;
exports.getLastPage = getLastPage;
exports.getNeedLoginPages = getNeedLoginPages;
