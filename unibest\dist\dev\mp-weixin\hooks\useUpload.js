"use strict";
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;
var __spreadValues = (a, b) => {
  for (var prop in b || (b = {}))
    if (__hasOwnProp.call(b, prop))
      __defNormalProp(a, prop, b[prop]);
  if (__getOwnPropSymbols)
    for (var prop of __getOwnPropSymbols(b)) {
      if (__propIsEnum.call(b, prop))
        __defNormalProp(a, prop, b[prop]);
    }
  return a;
};
var __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const utils_index = require("../utils/index.js");
const VITE_UPLOAD_BASEURL = `${utils_index.getEnvBaseUploadUrl()}`;
function useUpload(options = {}) {
  const {
    formData = {},
    maxSize = 5 * 1024 * 1024,
    accept = ["*"],
    fileType = "image",
    success,
    error: onError
  } = options;
  const loading = common_vendor.ref(false);
  const error = common_vendor.ref(null);
  const data = common_vendor.ref(null);
  const handleFileChoose = ({ tempFilePath, size }) => {
    if (size > maxSize) {
      common_vendor.index.showToast({
        title: `文件大小不能超过 ${maxSize / 1024 / 1024}MB`,
        icon: "none"
      });
      return;
    }
    loading.value = true;
    uploadFile({
      tempFilePath,
      formData,
      onSuccess: (res) => {
        const { data: _data } = JSON.parse(res);
        data.value = _data;
        success == null ? void 0 : success(_data);
      },
      onError: (err) => {
        error.value = err;
        onError == null ? void 0 : onError(err);
      },
      onComplete: () => {
        loading.value = false;
      }
    });
  };
  const run = () => {
    const chooseFileOptions = {
      count: 1,
      success: (res) => {
        console.log("File selected successfully:", res);
        let tempFilePath = "";
        let size = 0;
        tempFilePath = res.tempFiles[0].tempFilePath;
        size = res.tempFiles[0].size;
        handleFileChoose({ tempFilePath, size });
      },
      fail: (err) => {
        console.error("File selection failed:", err);
        error.value = err;
        onError == null ? void 0 : onError(err);
      }
    };
    if (fileType === "image") {
      common_vendor.index.chooseMedia(__spreadProps(__spreadValues({}, chooseFileOptions), {
        mediaType: ["image"]
      }));
    } else {
      common_vendor.index.chooseFile(__spreadProps(__spreadValues({}, chooseFileOptions), {
        type: "all"
      }));
    }
  };
  return { loading, error, data, run };
}
function uploadFile(_0) {
  return __async(this, arguments, function* ({
    tempFilePath,
    formData,
    onSuccess,
    onError,
    onComplete
  }) {
    common_vendor.index.uploadFile({
      url: VITE_UPLOAD_BASEURL,
      filePath: tempFilePath,
      name: "file",
      formData,
      success: (uploadFileRes) => {
        try {
          const data = uploadFileRes.data;
          onSuccess(data);
        } catch (err) {
          onError(err);
        }
      },
      fail: (err) => {
        console.error("Upload failed:", err);
        onError(err);
      },
      complete: onComplete
    });
  });
}
exports.useUpload = useUpload;
