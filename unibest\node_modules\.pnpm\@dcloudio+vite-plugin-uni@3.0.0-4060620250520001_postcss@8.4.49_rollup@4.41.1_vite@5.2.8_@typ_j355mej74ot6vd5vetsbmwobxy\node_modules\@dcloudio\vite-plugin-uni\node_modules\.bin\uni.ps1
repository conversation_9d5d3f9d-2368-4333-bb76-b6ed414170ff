#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\Users\<USER>\Desktop\less-work-2023\unibest-tran\unibest\node_modules\.pnpm\@dcloudio+vite-plugin-uni@3.0.0-4060620250520001_postcss@8.4.49_rollup@4.41.1_vite@5.2.8_@typ_j355mej74ot6vd5vetsbmwobxy\node_modules\@dcloudio\vite-plugin-uni\bin\node_modules;C:\Users\<USER>\Desktop\less-work-2023\unibest-tran\unibest\node_modules\.pnpm\@dcloudio+vite-plugin-uni@3.0.0-4060620250520001_postcss@8.4.49_rollup@4.41.1_vite@5.2.8_@typ_j355mej74ot6vd5vetsbmwobxy\node_modules\@dcloudio\vite-plugin-uni\node_modules;C:\Users\<USER>\Desktop\less-work-2023\unibest-tran\unibest\node_modules\.pnpm\@dcloudio+vite-plugin-uni@3.0.0-4060620250520001_postcss@8.4.49_rollup@4.41.1_vite@5.2.8_@typ_j355mej74ot6vd5vetsbmwobxy\node_modules\@dcloudio\node_modules;C:\Users\<USER>\Desktop\less-work-2023\unibest-tran\unibest\node_modules\.pnpm\@dcloudio+vite-plugin-uni@3.0.0-4060620250520001_postcss@8.4.49_rollup@4.41.1_vite@5.2.8_@typ_j355mej74ot6vd5vetsbmwobxy\node_modules;C:\Users\<USER>\Desktop\less-work-2023\unibest-tran\unibest\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@dcloudio+vite-plugin-uni@3.0.0-4060620250520001_postcss@8.4.49_rollup@4.41.1_vite@5.2.8_@typ_j355mej74ot6vd5vetsbmwobxy/node_modules/@dcloudio/vite-plugin-uni/bin/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@dcloudio+vite-plugin-uni@3.0.0-4060620250520001_postcss@8.4.49_rollup@4.41.1_vite@5.2.8_@typ_j355mej74ot6vd5vetsbmwobxy/node_modules/@dcloudio/vite-plugin-uni/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@dcloudio+vite-plugin-uni@3.0.0-4060620250520001_postcss@8.4.49_rollup@4.41.1_vite@5.2.8_@typ_j355mej74ot6vd5vetsbmwobxy/node_modules/@dcloudio/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@dcloudio+vite-plugin-uni@3.0.0-4060620250520001_postcss@8.4.49_rollup@4.41.1_vite@5.2.8_@typ_j355mej74ot6vd5vetsbmwobxy/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../../bin/uni.js" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../../bin/uni.js" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../../bin/uni.js" $args
  } else {
    & "node$exe"  "$basedir/../../bin/uni.js" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
