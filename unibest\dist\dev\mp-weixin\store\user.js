"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../common/vendor.js");
const _loginRes = common_vendor.index.getStorageSync("loginRes") ? common_vendor.index.getStorageSync("loginRes") : {};
const useUserStore = common_vendor.defineStore(
  "user",
  () => {
    const loginRes = common_vendor.ref(_loginRes);
    const clientId = common_vendor.ref("e5cd7e4891bf95d1d19206ce24a7b32e");
    const grantType = common_vendor.ref("xcx");
    const tenantId = common_vendor.ref("000000");
    const accessToken = common_vendor.computed(() => {
      return loginRes.value.access_token;
    });
    const isLogin = common_vendor.computed(() => !!loginRes.value.access_token);
    function saveLoginRes(obj) {
      loginRes.value = obj;
      common_vendor.index.setStorageSync("loginRes", obj);
    }
    function clearStorage() {
      loginRes.value = {};
      common_vendor.index.removeStorageSync("loginRes");
    }
    const login = (data) => __async(exports, null, function* () {
      saveLoginRes(data);
    });
    const logout = () => {
      clearStorage();
    };
    return {
      loginRes,
      clientId,
      grantType,
      tenantId,
      accessToken,
      isLogin,
      saveLoginRes,
      clearStorage,
      login,
      logout
    };
  },
  {
    persist: {
      key: "user"
    }
  }
);
exports.useUserStore = useUserStore;
