import { VueQueryPlugin } from '@tanstack/vue-query'
import { createSSRApp } from 'vue'
import App from './App.vue'
import { requestInterceptor } from './http/interceptor'
import { routeInterceptor } from './router/interceptor'
import WotDesign from 'wot-design-uni'

import store from './store'
import '@/style/index.scss'
import '@/style/atomic.css'
import '@/static/iconfont/iconfont.css'
import 'virtual:uno.css'

export function createApp() {
  const app = createSSRApp(App)
  app.use(store)
  app.use(routeInterceptor)
  app.use(requestInterceptor)
  app.use(VueQueryPlugin)
  app.use(WotDesign)

  return {
    app,
  }
}
