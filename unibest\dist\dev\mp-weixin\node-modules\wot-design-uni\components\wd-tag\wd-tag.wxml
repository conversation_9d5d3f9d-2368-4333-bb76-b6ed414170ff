<view class="{{['data-v-14921b29', t]}}" style="{{v}}" bindtap="{{w}}"><view wx:if="{{a}}" class="wd-tag__icon data-v-14921b29"><slot name="icon"/></view><wd-icon wx:elif="{{b}}" class="data-v-14921b29" u-i="14921b29-0" bind:__l="__l" u-p="{{c}}"/><view class="wd-tag__text data-v-14921b29" style="{{d}}"><slot/></view><view wx:if="{{e}}" class="wd-tag__close data-v-14921b29" catchtap="{{g}}"><wd-icon wx:if="{{f}}" class="data-v-14921b29" u-i="14921b29-1" bind:__l="__l" u-p="{{f}}"/></view><input wx:if="{{h}}" class="wd-tag__add-text data-v-14921b29" placeholder="{{i}}" type="text" focus="{{true}}" bindblur="{{j}}" bindconfirm="{{k}}" value="{{l}}" bindinput="{{m}}"/><view wx:elif="{{n}}" class="wd-tag__text data-v-14921b29" style="{{r}}" catchtap="{{s}}"><slot wx:if="{{o}}" name="add"></slot><block wx:else><wd-icon wx:if="{{p}}" class="data-v-14921b29" u-i="14921b29-2" bind:__l="__l" u-p="{{p}}"/><text class="data-v-14921b29">{{q}}</text></block></view></view>