#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/vite@5.2.8_@types+node@20.17.9_sass@1.77.8_terser@5.36.0/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/vite@5.2.8_@types+node@20.17.9_sass@1.77.8_terser@5.36.0/node_modules/vite/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/vite@5.2.8_@types+node@20.17.9_sass@1.77.8_terser@5.36.0/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/vite@5.2.8_@types+node@20.17.9_sass@1.77.8_terser@5.36.0/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/vite@5.2.8_@types+node@20.17.9_sass@1.77.8_terser@5.36.0/node_modules/vite/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/vite@5.2.8_@types+node@20.17.9_sass@1.77.8_terser@5.36.0/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vite@5.2.8_@types+node@20.17.9_sass@1.77.8_terser@5.36.0/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../../vite@5.2.8_@types+node@20.17.9_sass@1.77.8_terser@5.36.0/node_modules/vite/bin/vite.js" "$@"
fi
