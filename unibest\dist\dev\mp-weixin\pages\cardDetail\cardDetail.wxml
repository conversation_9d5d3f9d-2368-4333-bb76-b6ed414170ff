<layout-default-uni class="data-v-d1a3008d" u-s="{{['d']}}" u-i="d1a3008d-0" bind:__l="__l"><view class="height-100vh flex flex-column box-border page-wrap data-v-d1a3008d"><view class="position-relative bgWrap zindex-5 flex-1 flex data-v-d1a3008d"><view class="position-relative bg-content flex-1 width-100 data-v-d1a3008d"><view class="position-absolute left-0 top-0 right-0 bottom-0 video-wrap data-v-d1a3008d"><view wx:if="{{a}}" class="{{['video-box', 'img', 'data-v-d1a3008d', c]}}"><image src="{{b}}" lazy-load="false" mode="aspectFill" class="width-100 height-100 data-v-d1a3008d"></image></view><image wx:else src="{{d}}" lazy-load="false" mode="aspectFill" class="width-100 height-100 data-v-d1a3008d"></image><view wx:if="{{e}}" class="video-box video data-v-d1a3008d"><video id="myVideo" src="{{f}}" autoplay enable-play-gesture object-fit="cover" poster="{{g}}" class="width-100 height-100 data-v-d1a3008d" bindplay="{{h}}" bindended="{{i}}" bindpause="{{j}}" bindloadedmetadata="{{k}}"></video></view></view></view><view class="position-absolute left-0 top-0 right-0 bottom-0 zindex-10 text-white flex flex-column bg-content-main data-v-d1a3008d"><view class="flex align-center page-title-box flex-shrink text-center flex align-center justify-center position-relative data-v-d1a3008d"><view bindtap="{{l}}" class="mt ml flex-shrink font-12 px-2 py-1 position-absolute left-0 text-white data-v-d1a3008d"><view class="iconfont icon-fanhui data-v-d1a3008d"></view></view><text class="font-weight-500 font-m flex-1 data-v-d1a3008d">我的名片</text></view><view class="p-base flex-1 flex flex-column pt-5 data-v-d1a3008d"><view class="flex justify-between flex-shrink data-v-d1a3008d"><view class="logo-box mr-base data-v-d1a3008d"><image src="{{m}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view></view><view class="flex-1 flex flex-column justify-end data-v-d1a3008d"><block wx:if="{{n}}"><view wx:if="{{o}}" class="replay-box mb-2 data-v-d1a3008d" hover-class="bg-hover" bindtap="{{q}}"><image src="{{p}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view></block></view><view class="mb-auto flex-shrink flex align-center position-relative data-v-d1a3008d"><view class="more-box mr-4 data-v-d1a3008d"><image src="{{r}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view><view class="data-v-d1a3008d"><view class="font-weight-bold font-20 mb data-v-d1a3008d">{{s}}</view><view class="flex align-center font-14 data-v-d1a3008d">{{t}} <label class="mx-1 data-v-d1a3008d">|</label> {{v}}</view></view><button open-type="share" class="share-box position-absolute flex align-center justify-center font-12 data-v-d1a3008d"><view class="icon-box mr-0-8 flex algin-center data-v-d1a3008d"><image src="{{w}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view> 分享名片 </button></view></view></view></view><view class="p-4 width-100 box-border flex-shrink data-v-d1a3008d"><view class="scroll-row pb-4 overflow-x-auto no-scrollbar box-border data-v-d1a3008d"><view class="d-inline-block mr-4 data-v-d1a3008d" bindtap="{{A}}"><view class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item data-v-d1a3008d"><view class="flex align-center mb-1 data-v-d1a3008d"><view class="icon-box mr-0-8 data-v-d1a3008d"><image src="{{x}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view><view class="text-theme-brand font-14 data-v-d1a3008d">联系电话</view></view><view class="text-third width-100 text-ellipsis font-12 data-v-d1a3008d"><text class="mr-1 data-v-d1a3008d">{{y}}</text> {{z}}</view></view></view><view class="d-inline-block mr-4 data-v-d1a3008d" bindtap="{{D}}"><view class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item data-v-d1a3008d"><view class="flex align-center mb-1 data-v-d1a3008d"><view class="icon-box mr-0-8 data-v-d1a3008d"><image src="{{B}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view><view class="text-theme-brand font-14 data-v-d1a3008d">微信</view></view><view class="text-third width-100 text-ellipsis font-12 data-v-d1a3008d">{{C}}</view></view></view><view class="d-inline-block mr-4 data-v-d1a3008d" bindtap="{{G}}"><view class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item data-v-d1a3008d"><view class="flex align-center mb-1 data-v-d1a3008d"><view class="icon-box mr-0-8 data-v-d1a3008d"><image src="{{E}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view><view class="text-theme-brand font-14 data-v-d1a3008d">邮箱</view></view><view class="text-third width-100 text-ellipsis font-12 data-v-d1a3008d">{{F}}</view></view></view><view class="d-inline-block data-v-d1a3008d" bindtap="{{J}}"><view class="rounded-8 p-base box-border border flex flex-column justify-evenly contact-item data-v-d1a3008d"><view class="flex align-center mb-1 data-v-d1a3008d"><view class="icon-box mr-0-8 data-v-d1a3008d"><image src="{{H}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view><view class="text-theme-brand font-14 data-v-d1a3008d">公司地址</view></view><view class="text-third width-100 text-ellipsis font-12 data-v-d1a3008d">{{I}}</view></view></view></view><view class="text-third mb-4 font-12 data-v-d1a3008d">{{K}}</view><view class="scroll-row mb-4 overflow-x-auto no-scrollbar data-v-d1a3008d"><view wx:for="{{L}}" wx:for-item="item" wx:key="b" class="{{['rounded-80', 'py-1', 'px-3', 'text-second', 'bg-info', 'font-12', 'mr-base', 'mb', 'scroll-row-item', 'data-v-d1a3008d', M]}}" bindtap="{{item.c}}">{{item.a}}</view></view><view class="bg-theme-brand text-white text-center normal-button-rounded flex align-center justify-center font-14 data-v-d1a3008d" style="{{'margin-bottom:' + Q}}" bindtap="{{R}}"><view wx:if="{{N}}" class="loading-box data-v-d1a3008d"><image src="{{O}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view><view class="{{['flex', 'align-center', 'data-v-d1a3008d', P]}}"><text class="mx-0-8 data-v-d1a3008d">AI</text> 聊一聊 </view></view></view><my-modal wx:if="{{S}}" class="data-v-d1a3008d" u-s="{{['d']}}" bindclose="{{X}}" u-i="d1a3008d-1,d1a3008d-0" bind:__l="__l" u-p="{{Y}}"><view class="my-4 bg-input contact-input text-first font-weight-bold font-18 flex align-center justify-center data-v-d1a3008d"><text class="data-v-d1a3008d">{{T}}</text><view class="line data-v-d1a3008d"></view><text class="data-v-d1a3008d">{{U}}</text></view><view class="flex align-center justify-between data-v-d1a3008d"><view class="normal-button-rounded text-theme-brand border-current flex-1 font-14 data-v-d1a3008d" bindtap="{{V}}">复制号码</view><view class="mx-2 flex-shrink data-v-d1a3008d"></view><view class="normal-button-rounded bg-theme-brand text-white flex-1 font-14 data-v-d1a3008d" bindtap="{{W}}">拨打号码</view></view></my-modal><my-modal wx:if="{{Z}}" class="data-v-d1a3008d" u-s="{{['titleLeft','d']}}" bindclose="{{ah}}" u-i="d1a3008d-2,d1a3008d-0" bind:__l="__l" u-p="{{ai}}"><view class="flex align-center font-14 data-v-d1a3008d" slot="titleLeft"><view class="text-<strong></strong>second mr-base data-v-d1a3008d">微信号</view><view class="text-first font-weight-bold mr-base data-v-d1a3008d">{{aa}}</view><view class="copyicon-box mr-1 data-v-d1a3008d" bindtap="{{ac}}"><image src="{{ab}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view></view><view class="pt-4 data-v-d1a3008d"><view class="flex align-center mb-4 data-v-d1a3008d"><view class="avatar-box mr-4 flex-shrink data-v-d1a3008d"><image src="{{ad}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d"></image></view><view class="flex-1 data-v-d1a3008d"><view class="font-weight-bold text-first font-20 my-0-8 data-v-d1a3008d">{{ae}}</view><view class="text-second font-14 data-v-d1a3008d">{{af}}</view></view></view><view class="rounded-8 bg-info p-4 data-v-d1a3008d"><view class="rounded-8 bg-white p-4 mb-4 flex align-center data-v-d1a3008d"><view class="qrcode-box mx-auto data-v-d1a3008d"><image src="{{ag}}" mode="aspectFit" class="width-100 height-100 data-v-d1a3008d" show-menu-by-longpress></image></view></view><view class="text-placeholder font-12 text-center data-v-d1a3008d">扫描二维码，添加我的企业微信</view></view></view></my-modal><my-modal wx:if="{{aj}}" class="data-v-d1a3008d" u-s="{{['d']}}" bindclose="{{am}}" u-i="d1a3008d-3,d1a3008d-0" bind:__l="__l" u-p="{{an}}"><view class="my-4 bg-input contact-input text-first font-weight-bold font-18 flex align-center justify-center data-v-d1a3008d"><text class="data-v-d1a3008d">{{ak}}</text></view><view class="flex align-center justify-center data-v-d1a3008d"><view class="normal-button-rounded text-theme-brand border-current width-100 font-14 data-v-d1a3008d" bindtap="{{al}}">复制邮箱</view></view></my-modal><my-modal wx:if="{{ao}}" class="data-v-d1a3008d" u-s="{{['d']}}" bindclose="{{ax}}" u-i="d1a3008d-4,d1a3008d-0" bind:__l="__l" u-p="{{ay}}"><view class="my-4 bg-light-detail rounded-8 overflow-hidden data-v-d1a3008d"><view class="p-1-2 data-v-d1a3008d"><view class="text-first font-16 font-weight-bold mb-0-8 data-v-d1a3008d">{{ap}}</view><view class="text-second font-14 data-v-d1a3008d">{{aq}}</view></view><view class="address-box data-v-d1a3008d"><map class="data-v-d1a3008d" style="width:100%;height:100%" latitude="{{ar}}" longitude="{{as}}" markers="{{at}}"></map></view></view><view class="flex align-center justify-between data-v-d1a3008d"><view class="normal-button-rounded text-theme-brand border-current flex-1 font-14 data-v-d1a3008d" bindtap="{{av}}">复制地址</view><view class="mx-2 flex-shrink data-v-d1a3008d"></view><view class="normal-button-rounded bg-theme-brand text-white flex-1 font-14 data-v-d1a3008d" bindtap="{{aw}}">前端导航</view></view></my-modal></view></layout-default-uni>