"use strict";
const TABBAR_MAP = {
  NATIVE_TABBAR: 1,
  CUSTOM_TABBAR_WITHOUT_CACHE: 3
};
const selectedTabbarStrategy = TABBAR_MAP.NATIVE_TABBAR;
const tabbarList = [
  {
    iconPath: "static/tabbar/home.png",
    selectedIconPath: "static/tabbar/homeHL.png",
    pagePath: "pages/index/index",
    text: "首页",
    icon: "home",
    // 选用 UI 框架自带的 icon 时，iconType 为 uiLib
    iconType: "uiLib"
  },
  {
    iconPath: "static/tabbar/example.png",
    selectedIconPath: "static/tabbar/exampleHL.png",
    pagePath: "pages/about/about",
    text: "关于",
    icon: "i-carbon-code",
    // 注意 unocss 的图标需要在 页面上引入一下，或者配置到 unocss.config.ts 的 safelist 中
    iconType: "unocss"
  }
  // {
  //   pagePath: 'pages/my/index',
  //   text: '我的',
  //   icon: '/static/logo.svg',
  //   iconType: 'local',
  // },
  // {
  //   pagePath: 'pages/mine/index',
  //   text: '我的',
  //   icon: 'iconfont icon-my',
  //   iconType: 'iconfont',
  // },
];
exports.TABBAR_MAP = TABBAR_MAP;
exports.selectedTabbarStrategy = selectedTabbarStrategy;
exports.tabbarList = tabbarList;
