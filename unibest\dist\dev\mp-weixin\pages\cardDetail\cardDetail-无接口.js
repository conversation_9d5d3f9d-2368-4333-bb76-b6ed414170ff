"use strict";
var __async = (__this, __arguments, generator) => {
  return new Promise((resolve, reject) => {
    var fulfilled = (value) => {
      try {
        step(generator.next(value));
      } catch (e) {
        reject(e);
      }
    };
    var rejected = (value) => {
      try {
        step(generator.throw(value));
      } catch (e) {
        reject(e);
      }
    };
    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
    step((generator = generator.apply(__this, __arguments)).next());
  });
};
const common_vendor = require("../../common/vendor.js");
const common_assets = require("../../common/assets.js");
const api_config = require("../../api/config.js");
const utils_request = require("../../utils/request.js");
const store_user = require("../../store/user.js");
const mockCard = require("../../mock-card.js");
if (!Array) {
  const _component_layout_default_uni = common_vendor.resolveComponent("layout-default-uni");
  _component_layout_default_uni();
}
if (!Math) {
  myModal();
}
const myModal = () => "../../components/common/myModal.js";
const KEY_PHONE = "phoneNumber";
const KEY_WEIXIN = "weixin";
const KEY_EMAIL = "email";
const KEY_ADDRESS = "address";
const _sfc_main = {
  __name: "cardDetail-无接口",
  setup(__props) {
    const CONTACT_LIST = [
      { title: "联系电话", desc: "188-8888-8888", icon: common_assets._imports_3, key: KEY_PHONE },
      { title: "微信", desc: "lisa@2025", icon: common_assets._imports_4, key: KEY_WEIXIN },
      { title: "邮箱", desc: "<EMAIL>", icon: common_assets._imports_5, key: KEY_EMAIL },
      { title: "公司地址", desc: "浙江省嘉兴市秀洲区", icon: common_assets._imports_6, key: KEY_ADDRESS }
    ];
    const PREQUESTION_LIST = [
      { content: "怎样减脂最有效", role: "user", id: "0" },
      { content: "如何练出腹肌", role: "user", id: "1" },
      { content: "练多久能看到效果", role: "user", id: "2" }
    ];
    const userStore = store_user.useUserStore();
    const videoContext = common_vendor.ref({});
    const videoSrc = common_vendor.ref(
      "http://demo.easeidea.com/minio/easeidea-ai/2025/08/18/336293297-2a7b6ef3fce2d2d7b59632ec86ead526db1d7da00dc52b9d444908302420b788.mp4"
    );
    const videoImgSrc = common_vendor.ref("http://demo.easeidea.com/minio/easeidea-ai/2025/08/18/picture.png");
    const isPlay = common_vendor.ref(false);
    const loading = common_vendor.ref(false);
    const safeAreaInsets = common_vendor.ref({});
    const preQuestionList = common_vendor.ref(PREQUESTION_LIST);
    const contactList = common_vendor.ref(CONTACT_LIST);
    const form = common_vendor.reactive({
      phoneNumber: "18888888888",
      email: "<EMAIL>",
      weixin: "lisa@2025",
      address: "浙江省嘉兴市秀洲区",
      latitude: 39.909,
      longitude: 116.39742
    });
    const covers = common_vendor.ref([
      {
        id: 0,
        latitude: 39.909,
        longitude: 116.39742,
        width: 32,
        height: 32,
        iconPath: "/static/images/markerIcon.png"
      }
    ]);
    const phoneVisible = common_vendor.ref(false);
    const emailVisible = common_vendor.ref(false);
    const addressVisible = common_vendor.ref(false);
    const weixinVisible = common_vendor.ref(false);
    common_vendor.onReady(() => {
      videoContext.value = common_vendor.index.createVideoContext("myVideo");
      console.log("mockJson", mockCard.mockJson);
    });
    common_vendor.onLoad(() => {
      common_vendor.index.getSystemInfo({
        success: function(res) {
          safeAreaInsets.value = res.safeAreaInsets;
        }
      });
      common_vendor.index.login({
        // 拿code,请求接口，获取openid 。sessionKey 再请求下一个接口
        provider: "weixin",
        onlyAuthorize: "e0655474bf72b215776216bbd8a7e6c9",
        success: (loginRes) => {
          onLogin(loginRes.code);
        },
        fail: (err) => {
          common_vendor.index.showToast({
            icon: "none",
            title: err
          });
        },
        complete: () => {
          console.log("complete");
        }
      });
    });
    common_vendor.onShareAppMessage((e) => {
      let shareInfo = {
        path: "/pages/cardDetail/cardDetail",
        title: "Lisa - EaseGYM健身中心｜金牌教练",
        imageUrl: "/static/images/sharePic.png"
      };
      return shareInfo;
    });
    function onTogglePlayStatus(status) {
      isPlay.value = status;
    }
    function onLoadedmetadata(e) {
      console.log("load", e);
      onTogglePlayStatus(true);
    }
    function onPlay() {
      onTogglePlayStatus(true);
    }
    function onEnded() {
      onTogglePlayStatus(false);
    }
    function onPause() {
      onTogglePlayStatus(false);
    }
    function onReplay() {
      onTogglePlayStatus(true);
      videoContext.value.play();
    }
    function onLogin(code) {
      return __async(this, null, function* () {
        loading.value = true;
        const reqdata = {
          clientId: userStore.clientId,
          grantType: userStore.grantType,
          tenantId: userStore.tenantId,
          xcxCode: code
        };
        const codeRes = yield utils_request.http.post(api_config.apiMap.login.wxLogin, reqdata, { noAuth: true, isJson: true });
        loading.value = false;
        if (codeRes.code === 200) {
          userStore.saveLoginRes(codeRes.data);
        } else {
          common_vendor.index.showToast({
            title: codeRes.msg,
            icon: "none"
          });
        }
      });
    }
    function onOpenChat() {
      if (loading.value)
        return;
      common_vendor.index.navigateTo({
        url: "/pages/chat/chat"
      });
    }
    function onTogglePhoneTc(visible) {
      phoneVisible.value = visible;
    }
    function onToggleWeixinTc(visible) {
      weixinVisible.value = visible;
    }
    function onToggleEmailTc(visible) {
      emailVisible.value = visible;
    }
    function onToggleAddresTc(visible) {
      addressVisible.value = visible;
    }
    function onOpenContact(item = {}) {
      const key = item.key;
      switch (key) {
        case KEY_PHONE:
          onTogglePhoneTc(true);
          break;
        case KEY_WEIXIN:
          onToggleWeixinTc(true);
          break;
        case KEY_EMAIL:
          onToggleEmailTc(true);
          break;
        case KEY_ADDRESS:
          onToggleAddresTc(true);
          break;
      }
    }
    function onCallPhone() {
      onTogglePhoneTc(false);
      common_vendor.index.makePhoneCall({
        phoneNumber: form.phoneNumber
      });
    }
    function onOpenLocation() {
      onToggleAddresTc(false);
      common_vendor.index.openLocation({
        latitude: form.latitude,
        longitude: form.longitude,
        success: function() {
        }
      });
    }
    function onCopy(key) {
      let data = form[key];
      common_vendor.index.setClipboardData({
        data,
        success: function() {
          common_vendor.index.showToast({
            title: "复制成功",
            icon: "none"
          });
        }
      });
    }
    function onSelectPrequestion(item = {}) {
      if (loading.value)
        return;
      common_vendor.index.navigateTo({
        url: `/pages/chat/chat?question=${JSON.stringify(item)}`
      });
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_assets._imports_0$1,
        b: common_vendor.n(isPlay.value ? "animate__fadeOut" : "animate__fadeIn"),
        c: videoSrc.value,
        d: videoImgSrc.value,
        e: common_vendor.o(onPlay),
        f: common_vendor.o(onEnded),
        g: common_vendor.o(onPause),
        h: common_vendor.o(onLoadedmetadata),
        i: common_assets._imports_1$1,
        j: !isPlay.value
      }, !isPlay.value ? {
        k: common_assets._imports_0,
        l: common_vendor.o(onReplay)
      } : {}, {
        m: common_assets._imports_1,
        n: common_assets._imports_2$1,
        o: common_vendor.f(contactList.value, (item, index, i0) => {
          return common_vendor.e({
            a: item.icon,
            b: common_vendor.t(item.title),
            c: item.key === "phoneNumber"
          }, item.key === "phoneNumber" ? {} : {}, {
            d: common_vendor.t(item.desc),
            e: index,
            f: common_vendor.n(index === contactList.value.length - 1 ? "" : "mr-4"),
            g: common_vendor.o(($event) => onOpenContact(item), index)
          });
        }),
        p: common_vendor.f(preQuestionList.value, (item, index, i0) => {
          return {
            a: common_vendor.t(item.content),
            b: index,
            c: common_vendor.o(($event) => onSelectPrequestion(item), index)
          };
        }),
        q: common_vendor.n(loading.value ? "opacity-0_5" : ""),
        r: loading.value
      }, loading.value ? {
        s: common_assets._imports_2
      } : {}, {
        t: common_vendor.n(loading.value ? "opacity-0_5" : ""),
        v: safeAreaInsets.value.bottom + "px",
        w: common_vendor.o(onOpenChat),
        x: phoneVisible.value
      }, phoneVisible.value ? {
        y: common_vendor.t(form.phoneNumber),
        z: common_vendor.o(($event) => onCopy("phoneNumber")),
        A: common_vendor.o(onCallPhone),
        B: common_vendor.o(($event) => onTogglePhoneTc(false)),
        C: common_vendor.p({
          ["round-icon"]: "/static/images/<EMAIL>",
          title: "联系电话",
          desc: "这是我的手机号码，欢迎您来电垂询"
        })
      } : {}, {
        D: weixinVisible.value
      }, weixinVisible.value ? {
        E: common_vendor.t(form.weixin),
        F: common_assets._imports_8$1,
        G: common_vendor.o(($event) => onCopy("weixin")),
        H: common_assets._imports_7,
        I: common_assets._imports_8,
        J: common_vendor.o(($event) => onToggleWeixinTc(false)),
        K: common_vendor.p({
          ["custom-title"]: true
        })
      } : {}, {
        L: emailVisible.value
      }, emailVisible.value ? {
        M: common_vendor.t(form.email),
        N: common_vendor.o(($event) => onCopy("email")),
        O: common_vendor.o(($event) => onToggleEmailTc(false)),
        P: common_vendor.p({
          ["round-icon"]: "/static/images/<EMAIL>",
          title: "邮箱",
          desc: "期待收到您的来信"
        })
      } : {}, {
        Q: addressVisible.value
      }, addressVisible.value ? {
        R: common_vendor.t(form.address),
        S: common_vendor.t(form.address),
        T: form.latitude,
        U: form.longitude,
        V: covers.value,
        W: common_vendor.o(($event) => onCopy("address")),
        X: common_vendor.o(onOpenLocation),
        Y: common_vendor.o(($event) => onToggleAddresTc(false)),
        Z: common_vendor.p({
          ["round-icon"]: "/static/images/<EMAIL>",
          title: "公司地址",
          desc: "这是详细地址导航，欢迎您前往"
        })
      } : {});
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-6209742a"]]);
_sfc_main.__runtimeHooks = 2;
wx.createPage(MiniProgramPage);
