#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules/@antfu/eslint-config/bin/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules/@antfu/eslint-config/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules/@antfu/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules/@antfu/eslint-config/bin/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules/@antfu/eslint-config/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules/@antfu/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules:/mnt/c/Users/<USER>/Desktop/less-work-2023/unibest-tran/unibest/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules/@antfu/eslint-config/bin/index.js" "$@"
else
  exec node  "$basedir/../../../../../../@antfu+eslint-config@4.15.0_@unocss+eslint-plugin@66.2.3_eslint@9.29.0_jiti@2.4.2__typescript_eoxdoebi2q2afvv3drbvjpy7mm/node_modules/@antfu/eslint-config/bin/index.js" "$@"
fi
