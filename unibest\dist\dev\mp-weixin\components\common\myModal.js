"use strict";
const common_vendor = require("../../common/vendor.js");
const _sfc_main = {
  __name: "myModal",
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ["close"],
  setup(__props, { emit: __emit }) {
    const emit = __emit;
    function onClose() {
      emit("close");
    }
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: __props.visible
      }, __props.visible ? {
        b: common_vendor.o(() => {
        }),
        c: common_vendor.o(onClose)
      } : {});
    };
  }
};
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-b4d34b42"]]);
wx.createComponent(Component);
